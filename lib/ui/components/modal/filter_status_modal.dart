import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class FilterStatusModal extends ConsumerStatefulWidget {
  const FilterStatusModal({
    super.key,
    this.filterArg,
  });

  final FilterArg? filterArg;

  @override
  ConsumerState<FilterStatusModal> createState() => _FilterStatusModalState();
}

class _FilterStatusModalState extends ConsumerState<FilterStatusModal> {
  String? selectedLabel;

  List<Map<String, String>> orderStatuses = [
    {"Pending": "pending"},
    {"Confirmed": "confirmed"},
    {"Processing": "processing"},
    {"Accepted": "accepted"},
    {"Awaiting Pickup": "awaiting-pickup"},
    {"En Route": "en-route"},
    {"Arrived": "arrived"},
    {"Delivered": "delivered"},
  ];

  @override
  void initState() {
    super.initState();
    if (widget.filterArg != null) {
      selectedLabel = widget.filterArg?.filter;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          YBox(20),
          Row(
            children: [
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Filter Data", style: AppTypography.text14.medium),
                    YBox(4),
                    Text(
                      "Filter by following options.",
                      style: AppTypography.text12.copyWith(
                          color: AppColors.black.withValues(alpha: 0.45)),
                    ),
                  ],
                ),
              ),
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppSvgs.close,
                  height: Sizer.height(20),
                ),
              )
            ],
          ),
          YBox(16),
          Divider(
            color: AppColors.neutral4,
            height: 1,
          ),
          YBox(24),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: List.generate(
              orderStatuses.length,
              (i) => Padding(
                padding: EdgeInsets.only(bottom: Sizer.height(12)),
                child: ListTileSelector(
                  title: orderStatuses[i].keys.first,
                  isSelected: selectedLabel == orderStatuses[i].values.first,
                  onTap: () {
                    selectedLabel = orderStatuses[i].values.first;
                    setState(() {});
                  },
                ),
              ),
            ),
          ),
          YBox(16),
          Row(
            children: [
              Expanded(
                child: CustomBtn.solid(
                  onTap: () {
                    Navigator.pop(context, FilterArg(filter: null));
                    selectedLabel = null;
                    setState(() {});
                  },
                  online: true,
                  isOutline: true,
                  textColor: AppColors.primaryBlue,
                  // outlineColor: AppColors.black.withValues(alpha: 0.45),
                  text: "Reset",
                ),
              ),
              XBox(12),
              Expanded(
                child: CustomBtn.solid(
                  onTap: () {
                    Navigator.pop(
                      context,
                      FilterArg(filter: selectedLabel),
                    );
                  },
                  online: selectedLabel != null,
                  text: "Show All",
                ),
              ),
            ],
          ),
          YBox(30),
        ],
      ),
    );
  }
}
