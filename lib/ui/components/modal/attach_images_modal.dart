import 'dart:io';

import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class AttachImagesModal extends ConsumerStatefulWidget {
  const AttachImagesModal({
    super.key,
    required this.deliveryId,
  });

  final String deliveryId;

  @override
  ConsumerState<AttachImagesModal> createState() => _AttachImagesModalState();
}

class _AttachImagesModalState extends ConsumerState<AttachImagesModal> {
  final List<File> _imageFiles = [];
  final List<String> _uploadedUrls = [];
  bool loadingImage = false;
  bool _uploadsComplete = false;
  @override
  Widget build(BuildContext context) {
    printty("_uploadedUrls $_uploadedUrls");
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          YBox(20),
          Row(
            children: [
              Text("Upload Images", style: AppTypography.text16.medium),
              Spacer(),
              InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                    color: AppColors.black,
                    size: Sizer.radius(24),
                  ))
            ],
          ),
          YBox(16),
          InkWell(
            onTap: _pickImages,
            child: Stack(
              children: [
                Container(
                  height: Sizer.height(100),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: AppColors.black.withValues(alpha: 0.1),
                      width: 1,
                    ),
                  ),
                  child: SvgPicture.asset(
                    AppSvgs.uploadDoc,
                    height: Sizer.height(100),
                    width: Sizer.screenWidth,
                  ),
                ),
              ],
            ),
          ),
          YBox(16),
          if (loadingImage)
            Center(
                child: LoaderIcon(
              color: AppColors.grey,
              size: Sizer.radius(30),
            ))
          else if (_imageFiles.isNotEmpty)
            Container(
              constraints: BoxConstraints(
                maxHeight: Sizer.height(200),
              ),
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: _imageFiles.length,
                separatorBuilder: (context, index) => YBox(12),
                itemBuilder: (context, index) {
                  final file = _imageFiles[index];
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // ClipRRect(
                          //   borderRadius: BorderRadius.circular(4),
                          //   child: Image.file(
                          //     file,
                          //     height: Sizer.height(50),
                          //     width: Sizer.height(50),
                          //     fit: BoxFit.cover,
                          //     errorBuilder: (context, error, stackTrace) {
                          //       return Container(
                          //         height: Sizer.height(50),
                          //         width: Sizer.height(50),
                          //         color: AppColors.neutral5,
                          //         child: Icon(
                          //           Icons.image,
                          //           color: AppColors.black,
                          //         ),
                          //       );
                          //     },
                          //   ),
                          // ),
                          SizedBox(
                            height: Sizer.height(40),
                            width: Sizer.height(40),
                            child: Image.asset(AppImages.oip),
                          ),
                          XBox(8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            " ${file.path.split('/').last}",
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                            style: AppTypography.text14,
                                          ),
                                          Text(
                                            "${(file.lengthSync() / 1024).toStringAsFixed(1)}KB",
                                            style: AppTypography.text12
                                                .withCustomColor(
                                              AppColors.black
                                                  .withValues(alpha: 0.45),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const XBox(40),
                                    InkWell(
                                      onTap: () {
                                        setState(() {
                                          _imageFiles.removeAt(index);
                                          _uploadedUrls.removeAt(index);
                                        });
                                      },
                                      child: Icon(
                                        Iconsax.trash,
                                        size: Sizer.radius(16),
                                        color: AppColors.blue8C,
                                      ),
                                    )
                                  ],
                                ),
                                YBox(6),
                                Row(
                                  children: [
                                    Expanded(
                                      child: LinearPercentIndicator(
                                        padding: EdgeInsets.zero,
                                        lineHeight: 8.0,
                                        percent: ref
                                            .watch(fileUploadVm)
                                            .getProgressForFile(file.path),
                                        backgroundColor: AppColors.yellowEC,
                                        progressColor: AppColors.yellowOA,
                                        barRadius: Radius.circular(8),
                                        animation: true,
                                        animationDuration: 300,
                                      ),
                                    ),
                                    XBox(12),
                                    Text(
                                      "${(ref.watch(fileUploadVm).getProgressForFile(file.path) * 100).toInt()}%",
                                      style: AppTypography.text12
                                          .withCustomColor(AppColors.gray700),
                                    )
                                  ],
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                },
              ),
            ),
          YBox(24),
          CustomBtn.solid(
            onTap: () async {
              final r = await ref.read(deliveryVmodel).attachImages(
                    id: widget.deliveryId,
                    documentUrls: _uploadedUrls,
                  );

              handleApiResponse(
                  response: r,
                  onSuccess: () {
                    Navigator.pop(context);
                    ref.read(deliveryVmodel).viewDelivery(widget.deliveryId);
                  });
            },
            online: _imageFiles.isNotEmpty,
            isLoading: ref.watch(deliveryVmodel).busy(attachImagesState),
            isOutline: true,
            textColor: AppColors.black.withValues(alpha: 0.45),
            outlineColor: AppColors.black.withValues(alpha: 0.45),
            text: "Confirm Images",
          ),
          YBox(30),
        ],
      ),
    );
  }

  Future<void> _pickImages() async {
    setState(() {
      loadingImage = true;
      _uploadsComplete = false;
    });

    try {
      // Reset progress tracking for any previous uploads
      ref.read(fileUploadVm).resetProgress();

      final pickedFiles = await ImagePickerUtils.pickImage(multiImage: true);

      for (var pickedFile in pickedFiles) {
        setState(() => _imageFiles.add(File(pickedFile.path)));
      }

      if (_imageFiles.isNotEmpty) {
        final r = await ref.read(fileUploadVm).uploadFile(file: _imageFiles);
        if (r.success && r.data != null && r.data!.isNotEmpty) {
          printty("upload complete ${r.data!.first.url}");
          _uploadedUrls.addAll(r.data!.map((e) => e.url ?? ""));
          setState(() {
            _uploadsComplete = true;
          });
        }
      }
    } catch (e) {
      FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.warning, message: e.toString());
    } finally {
      setState(() => loadingImage = false);
    }
  }
}
