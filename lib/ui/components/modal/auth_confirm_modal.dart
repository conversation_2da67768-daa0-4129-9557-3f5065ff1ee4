import 'package:builder_konnect_mgt/core/core.dart';

class AuthConfirmModal extends StatelessWidget {
  const AuthConfirmModal({
    super.key,
    required this.title,
    required this.desc,
    this.iconPath,
    this.confirmText,
    this.onConfirm,
  });

  final String title;
  final String desc;
  final String? iconPath;
  final String? confirmText;
  final VoidCallback? onConfirm;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
          vertical: Sizer.height(20),
        ),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SvgPicture.asset(iconPath ?? AppSvgs.checkCircle),
                const XBox(16),
                Expanded(
                  child: <PERSON>umn(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTypography.text16.medium.withCustomColor(
                          AppColors.black.withValues(alpha: 0.85),
                        ),
                      ),
                      YBox(8),
                      Text(
                        desc,
                        style: AppTypography.text14
                            .withCustomHeight(1.5)
                            .withCustomColor(
                              AppColors.black.withValues(alpha: 0.45),
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const YBox(24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: onConfirm,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(16),
                      vertical: Sizer.height(5),
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      confirmText ?? "Confirm",
                      style: AppTypography.text14.withCustomColor(Colors.white),
                    ),
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
