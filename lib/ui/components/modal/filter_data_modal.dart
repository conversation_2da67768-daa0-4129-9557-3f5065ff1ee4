import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class FilterDataModal extends ConsumerStatefulWidget {
  const FilterDataModal({
    super.key,
    this.filterArg,
  });

  final FilterArg? filterArg;
  // final Function()? onReset;

  @override
  ConsumerState<FilterDataModal> createState() => _FilterDataModalState();
}

class _FilterDataModalState extends ConsumerState<FilterDataModal> {
  String? selectedLabel;
  DateTime? selectedDate;
  DateTime? endDate;
  bool isCustomdate = false;

  @override
  void initState() {
    super.initState();
    if (widget.filterArg != null) {
      selectedLabel = widget.filterArg!.filter;
    }
  }

  // Format date to display as "Mon, Aug 17"
  String _formatDate(DateTime date) {
    final List<String> weekdays = [
      'Mon',
      'Tue',
      'Wed',
      'Thu',
      'Fri',
      'Sat',
      'Sun'
    ];
    final List<String> months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];

    final weekday =
        weekdays[date.weekday - 1]; // weekday is 1-7 where 1 is Monday
    final month = months[date.month - 1]; // month is 1-12
    final day = date.day;

    return '$weekday, $month $day';
  }

  // Show custom date picker dialog
  Future<void> _showCustomDatePicker(BuildContext context) async {
    final result = await showDialog<Map<String, DateTime?>>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(Sizer.radius(16)),
          ),
          child: CustomDatePicker(
            initialDate: selectedDate ?? DateTime.now(),
            endDate: endDate,
            isRangeSelection: true,
            minDate: DateTime.now()
                .subtract(const Duration(days: 365)), // 1 year ago
            maxDate: DateTime.now()
                .add(const Duration(days: 365)), // 1 year from now
            onDateSelected: (startDate, rangeEndDate) {
              Navigator.of(context).pop({
                'startDate': startDate,
                'endDate': rangeEndDate,
              });
            },
          ),
        );
      },
    );

    if (result != null) {
      setState(() {
        selectedDate = result['startDate'];
        endDate = result['endDate'];
        isCustomdate = true;
        // Reset the predefined date options when custom date is selected
        selectedLabel = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final deliveryVm = ref.watch(deliveryVmodel);
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          YBox(20),
          Row(
            children: [
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Filter Data", style: AppTypography.text14.medium),
                    YBox(4),
                    Text(
                      "Filter by following options.",
                      style: AppTypography.text12.copyWith(
                          color: AppColors.black.withValues(alpha: 0.45)),
                    ),
                  ],
                ),
              ),
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppSvgs.close,
                  height: Sizer.height(20),
                ),
              )
            ],
          ),
          YBox(16),
          Divider(
            color: AppColors.neutral4,
            height: 1,
          ),
          YBox(24),
          Text("Date Added", style: AppTypography.text14.medium),
          YBox(16),
          LoadableContentBuilder(
            isBusy: deliveryVm.busy(getVehicleTypesState),
            loadingBuilder: (context) {
              return Skeletonizer(
                enabled: true,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: List.generate(
                    3,
                    (i) => Padding(
                      padding:
                          EdgeInsets.only(bottom: i < 2 ? Sizer.height(12) : 0),
                      child: ListTileSelector(
                        title: "Standard Delivery",
                        isSelected: i == 0,
                        onTap: () {},
                      ),
                    ),
                  ),
                ),
              );
            },
            contentBuilder: (context) {
              return Padding(
                padding: EdgeInsets.only(
                  left: Sizer.width(16),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: StatDataFilterType.values
                      .where((e) => e != StatDataFilterType.customDate)
                      .map((e) {
                    return Padding(
                      padding: EdgeInsets.only(
                          bottom: StatDataFilterType.values.indexOf(e) <
                                  StatDataFilterType.values.length - 2
                              ? Sizer.height(16)
                              : 0),
                      child: ListTileSelector(
                        title: e.label,
                        isSelected: selectedLabel == e.label,
                        // StatDataFilterType.values.indexOf(e) ==
                        //         selectedDateAddedIndex ||

                        onTap: () {
                          selectedLabel = e.label;
                          selectedDate = null;
                          endDate = null;
                          isCustomdate = false;
                          setState(() {});
                        },
                      ),
                    );
                  }).toList(),
                ),
              );
            },
          ),
          // YBox(16),
          // Row(
          //   children: [
          //     Text(
          //       "Date Range",
          //       style: AppTypography.text14.medium,
          //     ),
          //     XBox(8),
          //     Switch(
          //       value: isRangeSelection,
          //       onChanged: (value) {
          //         setState(() {
          //           isRangeSelection = value;
          //           if (!value) {
          //             // If turning off range selection, clear the end date
          //             endDate = null;
          //           }
          //         });
          //       },
          //       activeColor: AppColors.primaryBlue,
          //     ),
          //   ],
          // ),
          YBox(12),
          InkWell(
            onTap: () => _showCustomDatePicker(context),
            child: Row(
              children: [
                Text(
                  "Enter a Custom Date",
                  style: AppTypography.text16.withCustomColor(
                    selectedDate != null
                        ? AppColors.primaryBlue
                        : AppColors.gray500,
                  ),
                ),
                XBox(10),
                SvgPicture.asset(
                  AppSvgs.calender,
                  height: Sizer.height(20),
                  colorFilter: ColorFilter.mode(
                    selectedDate != null
                        ? AppColors.primaryBlue
                        : AppColors.gray500,
                    BlendMode.srcIn,
                  ),
                ),
              ],
            ),
          ),
          if (selectedDate != null) ...[
            YBox(8),
            Text(
              endDate != null
                  ? '${_formatDate(selectedDate!)} - ${_formatDate(endDate!)}'
                  : _formatDate(selectedDate!),
              style: AppTypography.text14.medium
                  .withCustomColor(AppColors.primaryBlue),
            ),
          ],
          YBox(24),
          Row(
            children: [
              Expanded(
                child: CustomBtn.solid(
                  onTap: () {
                    setState(() {
                      selectedDate = null;
                      endDate = null;
                      isCustomdate = false;
                      selectedLabel = null;
                    });
                    Navigator.pop(context, FilterArg(filter: "reset"));
                  },
                  online: true,
                  isOutline: true,
                  textColor: AppColors.primaryBlue,
                  // outlineColor: AppColors.black.withValues(alpha: 0.45),
                  text: "Reset",
                ),
              ),
              XBox(12),
              Expanded(
                child: CustomBtn.solid(
                  onTap: () {
                    Navigator.pop(
                        context,
                        FilterArg(
                            isCustom: isCustomdate,
                            filter: isCustomdate
                                ? "${selectedDate?.toIso8601String()}|${endDate?.toIso8601String()}"
                                : selectedLabel));
                  },
                  online: selectedLabel != null || selectedDate != null,
                  text: "Filter",
                ),
              ),
            ],
          ),
          YBox(30),
        ],
      ),
    );
  }
}
