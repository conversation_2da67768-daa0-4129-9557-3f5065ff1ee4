import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class UpdateTrackingModal extends ConsumerStatefulWidget {
  const UpdateTrackingModal({
    super.key,
    required this.deliveryId,
  });

  final String deliveryId;

  @override
  ConsumerState<UpdateTrackingModal> createState() =>
      _UpdateTrackingModalState();
}

class _UpdateTrackingModalState extends ConsumerState<UpdateTrackingModal> {
  List<String> trackingStatuses = [];
  int selectedStatusIndex = -1;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final r = await ref.read(deliveryVmodel).getTrackingStatuses();
      if (r.success) {
        trackingStatuses = r.data ?? [];
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final deliveryVm = ref.watch(deliveryVmodel);
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          YBox(20),
          Text("Update Tracking", style: AppTypography.text16.medium),
          YBox(24),
          LoadableContentBuilder(
              isBusy: deliveryVm.busy(getTrackingStatusesState),
              loadingBuilder: (context) {
                return CheckListState();
              },
              contentBuilder: (context) {
                return SizedBox(
                  height: Sizer.height(170),
                  child: ListView.separated(
                    itemBuilder: (_, i) {
                      return TrackerSelector(
                        title: trackingStatuses[i],
                        isSelected: selectedStatusIndex == i,
                        onTap: () {
                          selectedStatusIndex = i;
                          setState(() {});
                        },
                      );
                    },
                    separatorBuilder: (_, __) => YBox(12),
                    itemCount: trackingStatuses.length,
                  ),
                );
              }),
          YBox(24),
          deliveryVm.busy(updateTrackingState)
              ? BtnLoadState()
              : Row(
                  children: [
                    Expanded(
                      child: CustomBtn.solid(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        online: true,
                        isOutline: true,
                        textColor: AppColors.black.withValues(alpha: 0.45),
                        outlineColor: AppColors.black.withValues(alpha: 0.45),
                        text: "Cancel",
                      ),
                    ),
                    XBox(12),
                    Expanded(
                      child: CustomBtn.solid(
                        onTap: () async {
                          if (selectedStatusIndex >= 0 &&
                              selectedStatusIndex < trackingStatuses.length) {
                            final r = await ref
                                .read(deliveryVmodel)
                                .updateTracking(
                                  id: widget.deliveryId,
                                  status: trackingStatuses[selectedStatusIndex],
                                );

                            handleApiResponse(
                              response: r,
                              onSuccess: () {
                                Navigator.pop(context);
                                ref
                                    .read(deliveryVmodel)
                                    .viewDelivery(widget.deliveryId);
                              },
                            );
                          } else {
                            FlushBarToast.fLSnackBar(
                              snackBarType: SnackBarType.warning,
                              message: 'Please select a tracking status',
                            );
                          }
                        },
                        online: selectedStatusIndex >= 0,
                        text: "Save",
                      ),
                    ),
                  ],
                ),
          YBox(30),
        ],
      ),
    );
  }
}

class TrackerSelector extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback onTap;

  const TrackerSelector({
    super.key,
    required this.title,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        CustomCheckbox(
          onTap: onTap,
          isSelected: isSelected,
        ),
        XBox(10),
        Text(
          title,
          style: AppTypography.text14.withCustomColor(
            AppColors.black.withValues(alpha: 0.85),
          ),
        ),
      ],
    );
  }
}
