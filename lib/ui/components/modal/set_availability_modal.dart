import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class SetAvailabilityModal extends ConsumerStatefulWidget {
  const SetAvailabilityModal({
    super.key,
    this.selectedAvailability,
  });

  final AvailabilityType? selectedAvailability;

  @override
  ConsumerState<SetAvailabilityModal> createState() =>
      _SetAvailabilityModalState();
}

class _SetAvailabilityModalState extends ConsumerState<SetAvailabilityModal> {
  AvailabilityType selectedAvailability = AvailabilityType.available;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.selectedAvailability != null) {
        selectedAvailability = widget.selectedAvailability!;
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final deliveryVm = ref.watch(deliveryVmodel);
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          YBox(20),
          Text("Set Availability", style: AppTypography.text16.medium),
          YBox(24),
          LoadableContentBuilder(
              isBusy: deliveryVm.busy(getTrackingStatusesState),
              loadingBuilder: (context) {
                return CheckListState();
              },
              contentBuilder: (context) {
                return ListView.separated(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemBuilder: (_, i) {
                    return AvailabilityTile(
                      label: AvailabilityType.values[i].label,
                      color: AvailabilityType.values[i].color,
                      isOffline: AvailabilityType.values[i] ==
                          AvailabilityType.offline,
                      isSelected:
                          selectedAvailability == AvailabilityType.values[i],
                      onTap: () {
                        selectedAvailability = AvailabilityType.values[i];
                        setState(() {});
                      },
                    );
                  },
                  separatorBuilder: (_, __) => YBox(2),
                  itemCount: AvailabilityType.values.length,
                );
              }),
          YBox(24),
          ref.watch(authVmodel).busy("setAvailabilityState")
              ? BtnLoadState()
              : Row(
                  children: [
                    Expanded(
                      child: CustomBtn.solid(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        online: true,
                        isOutline: true,
                        textColor: AppColors.black.withValues(alpha: 0.45),
                        outlineColor: AppColors.black.withValues(alpha: 0.45),
                        text: "Cancel",
                      ),
                    ),
                    XBox(12),
                    Expanded(
                      child: CustomBtn.solid(
                        onTap: () async {
                          final r = await ref.read(authVmodel).updateProfile(
                              busyObjectName: "setAvailabilityState",
                              activityStatus: selectedAvailability.params);

                          handleApiResponse(
                            response: r,
                            onSuccess: () {
                              Navigator.pop(context);
                              ref.read(authVmodel).getUser();
                            },
                          );
                        },
                        text: "Save",
                      ),
                    ),
                  ],
                ),
          YBox(30),
        ],
      ),
    );
  }
}

class AvailabilityTile extends StatelessWidget {
  const AvailabilityTile({
    super.key,
    this.isOffline = false,
    this.isSelected = false,
    this.onTap,
    required this.label,
    required this.color,
  });

  final bool isOffline;
  final bool isSelected;
  final VoidCallback? onTap;
  final String label;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(10),
          horizontal: Sizer.width(8),
        ),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppColors.blue5 : AppColors.transparent,
          ),
          borderRadius: BorderRadius.circular(Sizer.radius(4)),
        ),
        child: Row(
          children: [
            isOffline
                ? Icon(
                    Icons.cancel_outlined,
                    size: Sizer.height(10),
                    color: AppColors.neutral7,
                  )
                : AvailabilityIndicator(
                    color: color,
                  ),
            XBox(4),
            Expanded(
              child: Text(
                label,
                style: AppTypography.text14.withCustomColor(AppColors.neutral9),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
