import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class ResolveIssuesModal extends ConsumerStatefulWidget {
  const ResolveIssuesModal({
    super.key,
    required this.deliveryId,
  });

  final String deliveryId;

  @override
  ConsumerState<ResolveIssuesModal> createState() => _ResolveIssuesModalState();
}

class _ResolveIssuesModalState extends ConsumerState<ResolveIssuesModal> {
  final commentC = TextEditingController();
  final commentF = FocusNode();

  @override
  void dispose() {
    commentC.dispose();
    commentF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: Sizer.width(16),
        right: Sizer.width(16),
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          YBox(20),
          Text("Resolve Issues", style: AppTypography.text16.medium),
          YBox(24),
          CustomTextField(
            controller: commentC,
            focusNode: commentF,
            showLabelHeader: true,
            labelText: "Comments",
            hintText: "Enter your comments",
            maxLines: 4,
            onChanged: (p0) => setState(() {}),
          ),
          YBox(24),
          ref.watch(deliveryVmodel).busy(resolveIssuesState)
              ? BtnLoadState()
              : Row(
                  children: [
                    Expanded(
                      child: CustomBtn.solid(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        online: true,
                        isOutline: true,
                        textColor: AppColors.black.withValues(alpha: 0.45),
                        outlineColor: AppColors.black.withValues(alpha: 0.45),
                        text: "Cancel",
                      ),
                    ),
                    XBox(12),
                    Expanded(
                      child: CustomBtn.solid(
                        onTap: () async {
                          FocusScope.of(context).unfocus();
                          final r =
                              await ref.watch(deliveryVmodel).resolveIssues(
                                    id: widget.deliveryId,
                                    comment: commentC.text.trim(),
                                  );

                          handleApiResponse(
                              response: r,
                              onSuccess: () {
                                Navigator.pop(context);
                                ref
                                    .read(deliveryVmodel)
                                    .viewDelivery(widget.deliveryId);
                              });
                        },
                        online: commentC.text.isNotEmpty,
                        text: "Send To Admin",
                      ),
                    ),
                  ],
                ),
          YBox(30),
        ],
      ),
    );
  }
}
