import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class AssignDeliveryModal extends ConsumerStatefulWidget {
  const AssignDeliveryModal({
    super.key,
    required this.deliveryId,
  });

  final String deliveryId;

  @override
  ConsumerState<AssignDeliveryModal> createState() =>
      _AssignDeliveryModalState();
}

class _AssignDeliveryModalState extends ConsumerState<AssignDeliveryModal> {
  final searchC = TextEditingController();
  final searchF = FocusNode();

  RiderArg? riderArg;
  List<String> vehicleTypes = [];
  int selectedVehicleTypeIndex = 0;
  bool isSearching = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final r = await ref.read(deliveryVmodel).getVehicleTypes();
      if (r.success) {
        vehicleTypes = r.data ?? [];
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    searchC.dispose();
    searchF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final deliveryVm = ref.watch(deliveryVmodel);
    final riderVm = ref.watch(riderVmodel);
    return Padding(
      padding: EdgeInsets.only(
        left: Sizer.width(16),
        right: Sizer.width(16),
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          YBox(20),
          Text("Assign Delivery", style: AppTypography.text16.medium),
          YBox(24),
          Text("Rider’s Name", style: AppTypography.text14),
          YBox(8),
          Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: AppColors.neutral5,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomTextField(
                  controller: searchC,
                  focusNode: searchF,
                  hideBorder: true,
                  hintText: "Enter",
                  prefixIcon: Icon(
                    Icons.search,
                    size: Sizer.radius(16),
                    color: AppColors.black.withValues(alpha: 0.85),
                  ),
                  onChanged: (p0) {
                    isSearching = p0.isNotEmpty;
                    riderVm.getRiders(query: p0.trim());
                    setState(() {});
                  },
                ),
                AnimatedSize(
                  duration: const Duration(milliseconds: 400),
                  child: isSearching
                      ? riderVm.isBusy
                          ? SizedBox(
                              height: Sizer.height(120),
                              child: ListView.separated(
                                padding: EdgeInsets.only(
                                  top: Sizer.height(10),
                                  bottom: Sizer.height(40),
                                  left: Sizer.width(16),
                                  right: Sizer.width(16),
                                ),
                                shrinkWrap: true,
                                itemBuilder: (_, i) {
                                  return Skeletonizer(
                                    enabled: true,
                                    child: Text(
                                      "Rider name name name",
                                      style:
                                          AppTypography.text16.withCustomColor(
                                        AppColors.black.withValues(alpha: 0.85),
                                      ),
                                    ),
                                  );
                                },
                                separatorBuilder: (_, __) => YBox(12),
                                itemCount: 4,
                              ),
                            )
                          : SizedBox(
                              height: Sizer.height(120),
                              child: ListView.separated(
                                padding: EdgeInsets.only(
                                  top: Sizer.height(10),
                                  bottom: Sizer.height(40),
                                  left: Sizer.width(16),
                                  right: Sizer.width(16),
                                ),
                                shrinkWrap: true,
                                itemBuilder: (_, i) {
                                  final rider = riderVm.riderList[i];
                                  return InkWell(
                                    onTap: () {
                                      riderArg = RiderArg(
                                        name: rider.name ?? "",
                                        phone: rider.phone ?? "",
                                        email: rider.email ?? "",
                                      );
                                      isSearching = false;
                                      searchC.clear();
                                      FocusScope.of(context).unfocus();
                                      setState(() {});
                                    },
                                    child: Text(
                                      rider.name ?? "",
                                      style:
                                          AppTypography.text16.withCustomColor(
                                        AppColors.black.withValues(alpha: 0.85),
                                      ),
                                    ),
                                  );
                                },
                                separatorBuilder: (_, __) => YBox(12),
                                itemCount: riderVm.riderList.length,
                              ),
                            )
                      : const SizedBox.shrink(),
                )
              ],
            ),
          ),
          YBox(7),
          InkWell(
            onTap: () async {
              final riderArg = await ModalWrapper.bottomSheet(
                context: context,
                widget: NewRiderModal(),
              );

              if (riderArg is RiderArg) {
                this.riderArg = riderArg;
                setState(() {});
              }
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Icon(
                  Icons.add,
                  size: Sizer.radius(16),
                  color: AppColors.black.withValues(alpha: 0.85),
                ),
                XBox(10),
                Text(
                  "Add New Rider",
                  style: AppTypography.text14
                      .withCustomColor(AppColors.primaryBlue),
                ),
              ],
            ),
          ),
          YBox(24),
          if (riderArg != null)
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(16),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    riderArg?.name ?? "",
                    style: AppTypography.text16.withCustomColor(
                      AppColors.black.withValues(alpha: 0.85),
                    ),
                  ),
                  // YBox(4),
                  // Text(
                  //   riderArg?.email ?? "",
                  //   style: AppTypography.text12.withCustomColor(
                  //       AppColors.black.withValues(alpha: 0.45)),
                  // ),
                ],
              ),
            ),
          YBox(24),
          Text("Select Vehicle Type", style: AppTypography.text14.medium),
          YBox(16),
          LoadableContentBuilder(
              isBusy: deliveryVm.busy(getVehicleTypesState),
              loadingBuilder: (context) {
                return CheckListState();
              },
              contentBuilder: (context) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: vehicleTypes.map((e) {
                    return Padding(
                      padding: EdgeInsets.only(
                          bottom:
                              vehicleTypes.indexOf(e) < vehicleTypes.length - 1
                                  ? Sizer.height(12)
                                  : 0),
                      child: ListTileSelector(
                        title: e,
                        isSelected:
                            vehicleTypes.indexOf(e) == selectedVehicleTypeIndex,
                        onTap: () {
                          selectedVehicleTypeIndex = vehicleTypes.indexOf(e);
                          setState(() {});
                        },
                      ),
                    );
                  }).toList(),
                );
              }),
          YBox(24),
          if (ref.watch(deliveryVmodel).busy(assignDeliveryState))
            BtnLoadState()
          else
            Row(
              children: [
                Expanded(
                  child: CustomBtn.solid(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    online: true,
                    isOutline: true,
                    textColor: AppColors.black.withValues(alpha: 0.45),
                    outlineColor: AppColors.black.withValues(alpha: 0.45),
                    text: "Cancel",
                  ),
                ),
                XBox(12),
                Expanded(
                  child: CustomBtn.solid(
                    onTap: () async {
                      if (riderArg != null) {
                        final r = await ref.read(deliveryVmodel).assignDelivery(
                            id: widget.deliveryId,
                            riderArg: RiderArg(
                              name: riderArg?.name ?? "",
                              phone: riderArg?.phone ?? "",
                              email: riderArg?.email ?? "",
                              vehicleType:
                                  vehicleTypes[selectedVehicleTypeIndex],
                              bankId: riderArg?.bankId,
                              accountNumber: riderArg?.accountNumber,
                              accountName: riderArg?.accountName,
                            )

                            // name: riderArg?.name ?? "",
                            // phone: riderArg?.phone ?? "",
                            // email: riderArg?.email ?? "",
                            // vehicleType:
                            //     vehicleTypes[selectedVehicleTypeIndex],
                            );

                        handleApiResponse(
                            response: r,
                            onSuccess: () {
                              Navigator.pop(context);
                              ref
                                  .read(deliveryVmodel)
                                  .viewDelivery(widget.deliveryId);
                            });
                      } else {
                        FlushBarToast.fLSnackBar(
                          snackBarType: SnackBarType.warning,
                          message: 'Please select a rider',
                        );
                      }
                    },
                    online: true,
                    text: "Save",
                  ),
                ),
              ],
            ),
          YBox(30),
        ],
      ),
    );
  }
}
