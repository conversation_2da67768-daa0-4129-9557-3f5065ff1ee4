import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class ConfirmPackageModal extends ConsumerStatefulWidget {
  const ConfirmPackageModal({
    super.key,
    required this.deliveryId,
  });

  final String deliveryId;

  @override
  ConsumerState<ConfirmPackageModal> createState() =>
      _ConfirmPackageModalState();
}

class _ConfirmPackageModalState extends ConsumerState<ConfirmPackageModal> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: Sizer.width(16),
        right: Sizer.width(16),
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          YBox(20),
          Row(
            children: [
              Text("Confirm Package", style: AppTypography.text16.medium),
              Spacer(),
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Icon(
                  Icons.close,
                  color: AppColors.black,
                  size: Sizer.radius(24),
                ),
              )
            ],
          ),
          YBox(24),
          InkWell(
            onTap: () {},
            child: Stack(
              children: [
                Container(
                  height: Sizer.height(100),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: AppColors.black.withValues(alpha: 0.1),
                      width: 1,
                    ),
                  ),
                  child: SvgPicture.asset(
                    AppSvgs.uploadDoc,
                    height: Sizer.height(100),
                    width: Sizer.screenWidth,
                  ),
                ),
              ],
            ),
          ),
          YBox(24),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomCheckbox(
                onTap: () {},
                isSelected: false,
              ),
              XBox(8),
              Expanded(
                child: Text(
                  "I hereby confirm that I have received the package from the vendor, and upon inspection, I have verified that the items are correct, complete, and accurately match the customer’s order specifications. The package is ready for shipment.",
                  style:
                      AppTypography.text12.withCustomColor(AppColors.gray600),
                ),
              ),
            ],
          ),
          YBox(24),
          if (ref.watch(deliveryVmodel).busy(assignDeliveryState))
            BtnLoadState()
          else
            CustomBtn.solid(
              onTap: () {
                Navigator.pop(context);
              },
              online: true,
              isOutline: true,
              textColor: AppColors.black.withValues(alpha: 0.45),
              outlineColor: AppColors.black.withValues(alpha: 0.45),
              text: "Confirm Package",
            ),
          YBox(30),
        ],
      ),
    );
  }
}
