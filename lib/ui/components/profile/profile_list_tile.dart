import 'package:builder_konnect_mgt/core/core.dart';

class ProfileListTile extends StatelessWidget {
  const ProfileListTile({
    super.key,
    required this.title,
    this.trailingWidget,
    this.onTap,
  });

  final String title;
  final Widget? trailingWidget;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(10),
          horizontal: Sizer.width(8),
        ),
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.neutral4,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(Sizer.radius(8)),
        ),
        child: Row(
          children: [
            trailingWidget ??
                SvgPicture.asset(
                  AppSvgs.security,
                  height: Sizer.height(16),
                ),
            XBox(4),
            Text(
              title,
              style: AppTypography.text14.withCustomColor(AppColors.neutral9),
            ),
            const Spacer(),
            Icon(
              Icons.keyboard_arrow_right,
              color: AppColors.neutral9,
            )
          ],
        ),
      ),
    );
  }
}
