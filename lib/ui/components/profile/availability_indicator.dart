import 'package:builder_konnect_mgt/core/core.dart';

class AvailabilityIndicator extends StatelessWidget {
  const AvailabilityIndicator({
    super.key,
    this.color,
    this.isOffline = false,
  });

  final Color? color;
  final bool isOffline;

  @override
  Widget build(BuildContext context) {
    return isOffline
        ? Icon(
            Icons.cancel_outlined,
            size: Sizer.height(10),
            color: AppColors.neutral7,
          )
        : Container(
            margin: EdgeInsets.only(right: Sizer.width(6)),
            height: Sizer.height(8),
            width: Sizer.height(8),
            decoration: BoxDecoration(
              color: color ?? AppColors.green7,
              borderRadius: BorderRadius.circular(Sizer.radius(10)),
            ),
          );
  }
}
