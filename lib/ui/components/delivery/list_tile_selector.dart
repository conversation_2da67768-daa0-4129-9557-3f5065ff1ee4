import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class ListTileSelector extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback onTap;

  const ListTileSelector({
    super.key,
    required this.title,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Skeleton.replace(
            replacement: Bone.circle(
              size: Sizer.height(16),
            ),
            child: CustomRadioBtn(
              // onTap: onTap,
              isSelected: isSelected,
            ),
          ),
          XBox(10),
          Text(
            title,
            style: AppTypography.text14.withCustomColor(
              AppColors.black.withValues(alpha: 0.85),
            ),
          ),
        ],
      ),
    );
  }
}
