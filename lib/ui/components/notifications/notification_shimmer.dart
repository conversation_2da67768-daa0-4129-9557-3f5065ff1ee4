import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class NotificationShimmer extends StatelessWidget {
  const NotificationShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        children: [
          const YBox(40),
          // Date header shimmer
          Row(
            children: [
              Bone.circle(size: Sizer.height(16)),
              XBox(8),
              Bone.text(words: 1, fontSize: 14),
              XBox(8),
              Expanded(child: Bone.text(words: 1, fontSize: 1)),
            ],
          ),
          YBox(16),
          // Notification cards shimmer
          ...List.generate(
            10,
            (index) => Padding(
              padding: EdgeInsets.only(bottom: Sizer.height(16)),
              child: NotificationCard(
                notification: NotificationModel(
                  subject: "New Delivery Request",
                  message: "You have a new order notification",
                  createdAt: DateTime.now(),
                ),
                relativeTime: "2 mins ago",
              ),
            ),
          ),
        ],
      ),
    );
  }
}
