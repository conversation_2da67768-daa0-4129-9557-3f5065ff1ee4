import 'package:builder_konnect_mgt/core/core.dart';

class NotificationCard extends StatelessWidget {
  const NotificationCard({
    super.key,
    required this.notification,
    required this.relativeTime,
  });

  final NotificationModel notification;
  final String relativeTime;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                notification.subject ?? "",
                style: AppTypography.text14.medium
                    .withCustomColor(AppColors.blue4F),
              ),
              YBox(8),
              Text(
                notification.message ?? "",
                style: AppTypography.text12.withCustomColor(
                  AppColors.blue4F,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        XBox(8),
        Text(
          relativeTime,
          style: AppTypography.text12.withCustomColor(
            AppColors.gray500,
          ),
        ),
      ],
    );
  }
}
