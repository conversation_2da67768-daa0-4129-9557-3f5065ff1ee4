import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class CheckListState extends StatelessWidget {
  const CheckListState({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List.generate(
          5,
          (i) => Padding(
            padding: EdgeInsets.only(bottom: i < 4 ? Sizer.height(12) : 0),
            child: ListTileSelector(
              title: "Standard Delivery Delivery",
              isSelected: i == 0,
              onTap: () {},
            ),
          ),
        ),
      ),
    );
  }
}
