import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class StatShimmer extends StatelessWidget {
  const StatShimmer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Column(
        children: [
          Row<PERSON><PERSON>(),
          Y<PERSON><PERSON>(16),
          RowStat(),
        ],
      ),
    );
  }
}

class RowStat extends StatelessWidget {
  const RowStat({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: StatCard(
            title: "Total Orders",
            amount: "₦1.1 M",
            bgColor: AppColors.blueFF,
            borderColor: AppColors.blue5,
            amountColor: AppColors.primaryBlue,
            onTap: () {},
          ),
        ),
        XBox(16),
        Expanded(
          child: StatCard(
            title: "Pending Pending",
            amount: "18100",
            bgColor: AppColors.magentaF8,
            borderColor: AppColors.magenta4,
            borderButtomColor: AppColors.magenta2,
            amountColor: AppColors.magenta6,
            onTap: () {},
          ),
        ),
      ],
    );
  }
}
