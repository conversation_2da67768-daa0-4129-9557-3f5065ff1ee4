import 'package:builder_konnect_mgt/core/core.dart';

class TimelineWidget extends StatelessWidget {
  const TimelineWidget({
    super.key,
    required this.shortDesc,
    required this.longDesc,
    required this.time,
  });

  final String shortDesc;
  final String longDesc;
  final String time;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          // mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(AppSvgs.checkbox),
            Container(
              width: 1,
              height: Sizer.height(80),
              color: AppColors.primaryBlue,
            ),
          ],
        ),
        XBox(10),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                shortDesc,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: AppTypography.text14
                    .withCustomColor(AppColors.black.withValues(alpha: 0.85)),
              ),
              YBox(2),
              Text(
                longDesc,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: AppTypography.text12.withCustomColor(
                  AppColors.black.withValues(alpha: 0.45),
                ),
              ),
              YBox(4),
              Row(
                children: [
                  Text(
                    "MAR 12 2025",
                    style: AppTypography.text10.withCustomColor(
                      AppColors.black.withValues(alpha: 0.45),
                    ),
                  ),
                  XBox(8),
                  Container(
                    height: Sizer.height(8),
                    width: Sizer.width(8),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.neutral5,
                    ),
                  ),
                  XBox(8),
                  Text(
                    "12.04 PM",
                    style: AppTypography.text10.withCustomColor(
                      AppColors.black.withValues(alpha: 0.45),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        XBox(10),
        InkWell(
          onTap: () {
            // Show images in dialog
            showDialog(
              context: context,
              builder: (_) => Dialog(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Add images here
                    SizedBox(
                      height: Sizer.screenHeight * 0.6,
                      width: Sizer.screenWidth,
                      child: Image.asset(
                        AppImages.noimage,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
          child: Text(
            "View Images",
            style: AppTypography.text12.withCustomColor(AppColors.primaryBlue),
          ),
        ),
      ],
    );
  }
}
