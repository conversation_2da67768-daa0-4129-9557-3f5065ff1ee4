import 'package:builder_konnect_mgt/core/core.dart';

class OrderListTile extends StatelessWidget {
  const OrderListTile({
    super.key,
    required this.orderModel,
  });

  final OrderModel orderModel;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.pushNamed(
          context,
          RoutePath.orderDetailsScreen,
          arguments: orderModel.id,
        );
      },
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "#${orderModel.orderNumber}",
                  style: AppTypography.text14.medium
                      .withCustomColor(AppColors.primaryBlue),
                ),
                YBox(4),
                Text(
                  orderModel.customer?.name ?? "",
                  style: AppTypography.text14.medium.withCustomColor(
                    AppColors.gray500,
                  ),
                ),
              ],
            ),
          ),
          Column(
            children: [
              OrderStatus(status: orderModel.status ?? "Processing"),
              YBox(8),
              Text(
                AppUtils.dayWithSuffixMonthAndYear(
                  orderModel.createdAt ?? DateTime.now(),
                ),
                style: AppTypography.text12.withCustomColor(
                  AppColors.gray500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class OrderStatus extends StatefulWidget {
  const OrderStatus({
    super.key,
    required this.status,
  });

  final String status;

  @override
  State<OrderStatus> createState() => _OrderStatusState();
}

class _OrderStatusState extends State<OrderStatus> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(8),
        vertical: Sizer.height(2),
      ),
      decoration: BoxDecoration(
          color: getColor(widget.status)["bgColor"],
          borderRadius: BorderRadius.circular(Sizer.radius(2)),
          border: Border.all(
            color: getColor(widget.status)["borderColor"] ?? AppColors.yellow3,
          )),
      child: Text(
        widget.status.capitalizeFirst,
        style: AppTypography.text12.withCustomColor(
          getColor(widget.status)["textColor"] ?? AppColors.yellow6,
        ),
      ),
    );
  }

  Map<String, Color> getColor(String status) {
    final status = widget.status.toLowerCase();
    switch (status) {
      case "processing":
        return {
          "bgColor": AppColors.yellowE6,
          "textColor": AppColors.yellow6,
          "borderColor": AppColors.yellow3
        };
      case "pending":
        return {
          "bgColor": AppColors.red1,
          "textColor": AppColors.red6,
          "borderColor": AppColors.red3,
        };
      case "delivered":
        return {
          "bgColor": AppColors.greenED,
          "textColor": AppColors.green1A,
          "borderColor": AppColors.green4,
        };
      default:
        return {
          "bgColor": AppColors.yellowE6,
          "textColor": AppColors.yellow6,
          "borderColor": AppColors.yellow3,
        };
    }
  }
}
