import 'package:builder_konnect_mgt/core/core.dart';

class CostRowText extends StatelessWidget {
  const CostRowText({
    super.key,
    required this.leftText,
    required this.rightText,
  });

  final String leftText;
  final String rightText;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          leftText,
          style: AppTypography.text14.withCustomColor(
            AppColors.black.withValues(alpha: 0.45),
          ),
        ),
        XBox(30),
        Text(
          rightText,
          style: AppTypography.text14.medium.withCustomColor(
            AppColors.black..withValues(alpha: 0.45),
          ),
        ),
      ],
    );
  }
}
