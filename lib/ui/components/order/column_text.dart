import 'package:builder_konnect_mgt/core/core.dart';

class ColumnText extends StatelessWidget {
  const ColumnText(
      {super.key,
      required this.title,
      required this.subtitle,
      this.crossAxisAlignment});

  final String title;
  final String subtitle;
  final CrossAxisAlignment? crossAxisAlignment;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: crossAxisAlignment ?? CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTypography.text12.withCustomColor(
            AppColors.gray500,
          ),
        ),
        YBox(4),
        Text(
          subtitle,
          style: AppTypography.text14.medium,
        ),
      ],
    );
  }
}
