import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class SendMoneyAccountTile extends StatelessWidget {
  const SendMoneyAccountTile({
    super.key,
    this.showCheckBox = false,
    this.onTap,
  });

  final bool showCheckBox;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(
          Sizer.height(16),
        ),
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.neutral4,
          ),
          borderRadius: BorderRadius.circular(Sizer.radius(8)),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(
                Sizer.height(8),
              ),
              decoration: BoxDecoration(
                color: AppColors.dayBreakBlue,
                borderRadius: BorderRadius.circular(Sizer.radius(8)),
              ),
              child: SvgPicture.asset(AppSvgs.bank),
            ),
            XBox(13),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Sikiru Fatia",
                    style: AppTypography.text14.medium.withCustomColor(
                      AppColors.black.withValues(alpha: 0.85),
                    ),
                  ),
                  YBox(4),
                  Text(
                    "Access Bank | *********",
                    style: AppTypography.text12.withCustomColor(
                      AppColors.black.withValues(alpha: 0.45),
                    ),
                  ),
                ],
              ),
            ),
            if (showCheckBox)
              CustomRadioBtn(
                // onTap: onTap,
                isSelected: false,
              ),
          ],
        ),
      ),
    );
  }
}
