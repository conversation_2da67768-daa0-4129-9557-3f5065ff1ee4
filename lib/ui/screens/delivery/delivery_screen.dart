import 'dart:async';

import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/ui.dart';

class DeliveryScreen extends ConsumerStatefulWidget {
  const DeliveryScreen({super.key});

  @override
  ConsumerState<DeliveryScreen> createState() => _DeliveryScreenState();
}

class _DeliveryScreenState extends ConsumerState<DeliveryScreen> {
  final searchC = TextEditingController();
  final searchF = FocusNode();
  Timer? _debounce;

  FilterArg? _filterArg;
  String? _selectedStatus;

  @override
  void initState() {
    super.initState();
    // Add listener to update UI when text changes (for clear button visibility)
    searchC.addListener(() {
      setState(() {});
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Load initial deliveries
      ref.read(deliveryVmodel).fulfilmentDeliveries();
    });
  }

  @override
  void dispose() {
    searchC.dispose();
    searchF.dispose();
    _debounce?.cancel();

    super.dispose();
  }

  // Search deliveries with debounce
  void _searchDeliveries(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      ref.read(deliveryVmodel).fulfilmentDeliveries(
            query: query,
            dateFilter: _filterArg?.filter,
          );
    });
  }

  @override
  Widget build(BuildContext context) {
    final deliveryVm = ref.watch(deliveryVmodel);
    return Scaffold(
      appBar: CustomAppbar(
        title: "Deliveries",
        trailingWidget: InkWell(
          onTap: () {
            Navigator.pushNamed(context, RoutePath.notificationScreen);
          },
          child: SvgPicture.asset(AppSvgs.notification),
        ),
        leadingWidget: Container(
          height: Sizer.height(40),
          width: Sizer.width(40),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(Sizer.radius(40)),
              border: Border.all(
                color: AppColors.primaryBlue,
                width: 2,
              )),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(Sizer.radius(40)),
            child: ref.watch(authVmodel).user?.avatar != null
                ? MyCachedNetworkImage(
                    imageUrl: ref.watch(authVmodel).user!.avatar,
                    fit: BoxFit.cover,
                  )
                : Icon(
                    Iconsax.user,
                    size: Sizer.width(20),
                  ),
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            YBox(16),
            Text("Deliveries", style: AppTypography.text20.medium),
            Text(
              "View and manage all  orders",
              style: AppTypography.text14.withCustomColor(
                AppColors.black.withValues(alpha: 0.45),
              ),
            ),
            YBox(12),
            // if (deliveryVm.orderDeliveries.isNotEmpty)
            Row(
              children: [
                Expanded(
                  child: Skeleton.replace(
                    replacement: Bone(
                      height: Sizer.height(40),
                      width: Sizer.width(100),
                    ),
                    child: CustomTextField(
                      controller: searchC,
                      focusNode: searchF,
                      hintText: "Search with order no.",
                      onChanged: _searchDeliveries,
                      suffixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (searchC.text.isNotEmpty)
                            InkWell(
                              onTap: () {
                                searchC.clear();
                                _searchDeliveries('');
                              },
                              child: Padding(
                                padding: EdgeInsets.all(Sizer.width(10)),
                                child: Icon(
                                  Icons.close,
                                  size: Sizer.width(20),
                                  color: AppColors.gray500,
                                ),
                              ),
                            ),
                          InkWell(
                            onTap: () => _searchDeliveries(searchC.text),
                            child: Container(
                              padding: EdgeInsets.all(Sizer.width(10)),
                              decoration: BoxDecoration(
                                  border: Border(
                                left: BorderSide(
                                  color: AppColors.neutral5,
                                ),
                              )),
                              child: SvgPicture.asset(AppSvgs.search),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                XBox(12),
                Skeleton.replace(
                  replacement: Bone(
                    height: Sizer.height(40),
                    width: Sizer.width(40),
                  ),
                  child: InkWell(
                    onTap: () async {
                      final result = await ModalWrapper.bottomSheet(
                        context: context,
                        widget: FilterStatusModal(
                          filterArg: FilterArg(filter: _selectedStatus),
                        ),
                      );

                      if (result is FilterArg) {
                        printty("Selected status: ${result.filter}");
                        _selectedStatus = result.filter;
                        ref.read(deliveryVmodel).fulfilmentDeliveries(
                              status: result.filter,
                              query:
                                  searchC.text.isNotEmpty ? searchC.text : null,
                            );
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(16),
                        vertical: Sizer.height(10),
                      ),
                      decoration: BoxDecoration(
                          border: Border.all(
                        color: AppColors.neutral5,
                      )),
                      child: SvgPicture.asset(AppSvgs.filter),
                    ),
                  ),
                ),
              ],
            ),
            YBox(16),
            InkWell(
              onTap: () async {
                final result = await ModalWrapper.bottomSheet(
                  context: context,
                  widget: FilterDataModal(
                    filterArg: _filterArg,
                  ),
                );

                if (result is FilterArg) {
                  if (result.filter?.toLowerCase() == "reset" ||
                      result.filter?.toLowerCase() == "all" ||
                      result.filter == null) {
                    ref.read(deliveryVmodel).fulfilmentDeliveries();
                    _filterArg = null;
                    setState(() {});
                    return;
                  }
                  _filterArg = result;
                  ref.read(deliveryVmodel).fulfilmentDeliveries(
                        dateFilter: result.filter,
                        query: searchC.text.isNotEmpty ? searchC.text : null,
                      );
                }
              },
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(16),
                  vertical: Sizer.height(8),
                ),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.neutral5,
                  ),
                  borderRadius: BorderRadius.circular(Sizer.radius(4)),
                ),
                child: Row(
                  children: [
                    Text(
                      _filterArg?.isCustom == true
                          ? "Custom Date"
                          : _filterArg?.filter ?? "All",
                      style: AppTypography.text14,
                    ),
                    Spacer(),
                    Icon(
                      Icons.calendar_today_outlined,
                      size: 20,
                      color: AppColors.neutral9,
                    )
                  ],
                ),
              ),
            ),
            Expanded(
              child: LoadableContentBuilder(
                isBusy: deliveryVm.isBusy,
                items: deliveryVm.orderDeliveries,
                loadingBuilder: (context) {
                  return DeliveryShimmer(count: 10);
                },
                emptyBuilder: (context) {
                  return Center(
                    child: Text(
                      "No  deliveries found",
                      style: AppTypography.text14.copyWith(
                        fontWeight: FontWeight.w500,
                        color: AppColors.gray500,
                      ),
                    ),
                  );
                },
                contentBuilder: (context) {
                  return RefreshIndicator(
                    onRefresh: () async {
                      await ref.read(deliveryVmodel).fulfilmentDeliveries(
                            dateFilter: _filterArg?.filter,
                            query:
                                searchC.text.isNotEmpty ? searchC.text : null,
                          );
                    },
                    child: ListView.separated(
                      padding: EdgeInsets.only(
                          top: Sizer.height(24), bottom: Sizer.height(50)),
                      separatorBuilder: (_, __) => YBox(24),
                      itemCount: deliveryVm.orderDeliveries.length,
                      itemBuilder: (_, i) {
                        final o = deliveryVm.orderDeliveries[i];
                        return OrderListTile(orderModel: o);
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
