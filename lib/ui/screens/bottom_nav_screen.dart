import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/ui.dart';

class BottomNavScreen extends ConsumerStatefulWidget {
  const BottomNavScreen({
    super.key,
    this.args,
  });

  final DashArg? args;

  @override
  ConsumerState<BottomNavScreen> createState() => _BottomNavScreenState();
}

class _BottomNavScreenState extends ConsumerState<BottomNavScreen> {
  int currentIndex = 0;

  List<Map<String, Object>> screensMap = [
    {
      "name": "Dashboard",
      "screen": const DashboardScreen(),
      "iconPath": AppSvgs.dashboard,
    },
    // {
    //   "name": "Wallet",
    //   "screen": const WalletScreen(),
    //   "iconPath": AppSvgs.wallet,
    // },
    {
      "name": "Delivery",
      "screen": const DeliveryScreen(),
      "iconPath": AppSvgs.delivery,
    },
    {
      "name": "Profile",
      "screen": const ProfileScreen(),
      "iconPath": AppSvgs.profile,
    },
  ];

  @override
  void initState() {
    super.initState();
    if (widget.args?.index != null) {
      currentIndex = widget.args!.index!;
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  _init() async {
    await ref.read(authVmodel).getUser();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: BusyOverlay(
        show: false,
        child: Scaffold(
          body: screensMap[currentIndex]["screen"] as Widget,
          backgroundColor: AppColors.white,
          bottomNavigationBar: Container(
            height: Sizer.height(84),
            padding: EdgeInsets.only(
              bottom: Sizer.height(10),
            ),
            decoration: BoxDecoration(
                color: AppColors.gray100,
                border: Border(
                  top: BorderSide(
                    color: AppColors.grayF8,
                  ),
                )),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                ...screensMap.map((e) {
                  final index = screensMap.indexOf(e);
                  return BottomNavColumn(
                    icon: e["iconPath"],
                    isActive: currentIndex == index,
                    labelText: e["name"] as String,
                    onPressed: () {
                      currentIndex = index;
                      setState(() {});
                    },
                  );
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
