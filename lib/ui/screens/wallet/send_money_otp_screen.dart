import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class SendMoneyScreen extends ConsumerStatefulWidget {
  const SendMoneyScreen({super.key});

  @override
  ConsumerState<SendMoneyScreen> createState() => _SendMoneyScreenState();
}

class _SendMoneyScreenState extends ConsumerState<SendMoneyScreen> {
  final accountNumberC = TextEditingController();
  final bankNameC = TextEditingController();

  final accountNumberF = FocusNode();

  @override
  void dispose() {
    accountNumberC.dispose();
    bankNameC.dispose();
    accountNumberF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: "Send Money",
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        children: [
          YB<PERSON>(30),
          Text("Recipient Account",
              style: AppTypography.text14.withCustomColor(
                AppColors.black.withValues(alpha: 0.45),
              )),
          YBox(12),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(12),
              vertical: Sizer.height(12),
            ),
            decoration: BoxDecoration(
              color: AppColors.neutral2,
              borderRadius: BorderRadius.circular(Sizer.radius(8)),
              border: Border.all(color: AppColors.neutral5, width: 1),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextField(
                  controller: accountNumberC,
                  focusNode: accountNumberF,
                  showLabelHeader: true,
                  labelText: "Account Number",
                  hintText: "Enter Number",
                  fillColor: AppColors.white,
                  keyboardType: KeyboardType.phone,
                  onChanged: (p0) => setState(() {}),
                  onSubmitted: (value) {
                    accountNumberF.requestFocus();
                  },
                ),
                YBox(12),
                CustomTextField(
                  controller: bankNameC,
                  keyboardType: KeyboardType.email,
                  showLabelHeader: true,
                  isReadOnly: true,
                  labelText: "Select Bank",
                  hintText: "Select Bank",
                  fillColor: AppColors.white,
                  suffixIcon: Icon(
                    Icons.keyboard_arrow_down_rounded,
                    size: 20,
                    color: AppColors.neutral9,
                  ),
                  onChanged: (p0) => setState(() {}),
                  onSubmitted: (value) {
                    FocusScope.of(context).unfocus();
                  },
                ),
                YBox(16),
                CustomBtn.solid(
                  onTap: () {},
                  text: "Next",
                  isOutline: true,
                  outlineColor: AppColors.neutral5,
                  onlineColor: AppColors.transparent,
                  textColor: AppColors.black.withValues(alpha: 0.25),
                ),
              ],
            ),
          ),
          YBox(24),
          Text(
            "Recents Transactions",
            style: AppTypography.text14.withCustomColor(
              AppColors.black.withValues(alpha: 0.85),
            ),
          ),
          YBox(12),
          ListView.separated(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(
              bottom: Sizer.height(50),
            ),
            itemCount: 5, // Replace with actual number of transactions
            separatorBuilder: (_, __) => YBox(16),
            itemBuilder: (_, i) {
              return SendMoneyAccountTile(
                showCheckBox: true,
                onTap: () {
                  Navigator.pushNamed(
                      context, RoutePath.sendMoneyAccountScreen);
                },
              );
            },
          ),
        ],
      ),
    );
  }
}
