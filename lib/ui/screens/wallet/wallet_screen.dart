import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';
import 'package:flutter/services.dart';

class WalletScreen extends ConsumerStatefulWidget {
  const WalletScreen({super.key});

  @override
  ConsumerState<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends ConsumerState<WalletScreen> {
  // Mock data for wallet screen
  final String accountNumber = "**********";
  final String bankName = "Sterling Bank";
  final String walletBalance = "₦1.1 M";

  // Mock transaction data
  final List<Map<String, dynamic>> transactions = [
    {
      'type': 'credit',
      'title': 'Transfer received',
      'subtitle': 'Transfer from FATIA OLUWASEUN',
      'amount': 'N 20,000.00',
    },
    {
      'type': 'debit',
      'title': 'Cash Withdrawal',
      'subtitle': 'Transfer to FATIA OLUWASEUN',
      'amount': 'N 20,000.00',
    },
    {
      'type': 'credit',
      'title': 'Transfer received',
      'subtitle': 'Transfer from FATIA OLUWASEUN',
      'amount': 'N 20,000.00',
    },
    {
      'type': 'debit',
      'title': 'Cash Withdrawal',
      'subtitle': 'Transfer to FATIA OLUWASEUN',
      'amount': 'N 20,000.00',
    },
  ];

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Copied to clipboard'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Sizer.screenWidth,
      height: Sizer.screenHeight,
      child: BusyOverlay(
        show: ref.read(authVmodel).isBusy,
        child: Scaffold(
          appBar: CustomAppbar(
            title: "Wallet",
            trailingWidget: InkWell(
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.notificationScreen);
                },
                child: SvgPicture.asset(AppSvgs.notification)),
            leadingWidget: InkWell(
              child: Container(
                height: Sizer.height(40),
                width: Sizer.width(40),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(Sizer.radius(40)),
                    border: Border.all(
                      color: AppColors.primaryBlue,
                      width: 2,
                    )),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(Sizer.radius(40)),
                  child: ref.watch(authVmodel).user?.avatar != null
                      ? MyCachedNetworkImage(
                          imageUrl: ref.watch(authVmodel).user!.avatar,
                          fit: BoxFit.cover,
                        )
                      : Icon(
                          Iconsax.user,
                          size: Sizer.width(20),
                        ),
                ),
              ),
            ),
          ),
          body: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            children: [
              const YBox(16),
              // Wallet Card
              Container(
                padding: EdgeInsets.all(Sizer.width(16)),
                decoration: BoxDecoration(
                  color: AppColors.blueFF,
                  border: Border.all(
                    color: AppColors.blue5,
                    width: 1.5,
                  ),
                  borderRadius: BorderRadius.circular(Sizer.radius(8)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Bank Account Number with Copy Button
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(12),
                        vertical: Sizer.height(8),
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.blue8C,
                          width: 6,
                        ),
                        borderRadius: BorderRadius.circular(Sizer.radius(20)),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            "$bankName: $accountNumber",
                            style: AppTypography.text12.withCustomColor(
                              AppColors.primaryBlue,
                            ),
                          ),
                          const XBox(8),
                          InkWell(
                            onTap: () => _copyToClipboard(accountNumber),
                            child: Icon(
                              Icons.copy,
                              size: Sizer.width(16),
                              color: AppColors.primaryBlue,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const YBox(8),
                    // Wallet Balance
                    Text(
                      "Wallet Balance",
                      style: AppTypography.text12.withCustomColor(
                        AppColors.black.withValues(alpha: 0.45),
                      ),
                    ),
                    const YBox(4),
                    Text(
                      walletBalance,
                      style: AppTypography.text20.medium.withCustomColor(
                        AppColors.primaryBlue,
                      ),
                    ),
                    const YBox(16),
                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: WalletCta(
                            title: "Add Money",
                            onTap: () {},
                          ),
                        ),
                        const XBox(16),
                        Expanded(
                          child: WalletCta(
                            title: "Send Money",
                            onTap: () {
                              Navigator.pushNamed(
                                  context, RoutePath.sendMoneyScreen);
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const YBox(24),
              // Recent Transactions
              Text(
                "Recent Transactions",
                style: AppTypography.text20.medium,
              ),
              const YBox(4),
              Text(
                "View and manage all recent transactions",
                style: AppTypography.text14.withCustomColor(
                  AppColors.black.withValues(alpha: 0.45),
                ),
              ),
              const YBox(16),
              // Transaction List
              ...transactions
                  .map((transaction) => _buildTransactionItem(transaction)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionItem(Map<String, dynamic> transaction) {
    final bool isCredit = transaction['type'] == 'credit';

    return Container(
      margin: EdgeInsets.only(bottom: Sizer.height(16)),
      padding: EdgeInsets.symmetric(
        vertical: Sizer.height(8),
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.neutral4,
          ),
        ),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            isCredit ? AppSvgs.transfer : AppSvgs.withdraw,
          ),
          const XBox(8),
          // Transaction Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction['title'],
                  style: AppTypography.text14.medium.withCustomColor(
                    isCredit ? AppColors.success600 : AppColors.error500,
                  ),
                ),
                Text(
                  transaction['subtitle'],
                  style: AppTypography.text12.withCustomColor(
                    AppColors.black.withValues(alpha: 0.45),
                  ),
                ),
              ],
            ),
          ),
          // Transaction Amount
          Text(
            transaction['amount'],
            style: AppTypography.text14.medium.withCustomColor(
              isCredit ? AppColors.success500 : AppColors.error500,
            ),
          ),
        ],
      ),
    );
  }
}

class WalletCta extends StatelessWidget {
  const WalletCta({
    super.key,
    required this.title,
    this.onTap,
  });

  final String title;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(8),
        ),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(Sizer.radius(8)),
          border: Border.all(
            color: AppColors.blue8C,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Iconsax.add,
              size: Sizer.radius(16),
              color: AppColors.black,
            ),
            const XBox(8),
            Text(
              title,
              style: AppTypography.text16,
            ),
          ],
        ),
      ),
    );
  }
}
