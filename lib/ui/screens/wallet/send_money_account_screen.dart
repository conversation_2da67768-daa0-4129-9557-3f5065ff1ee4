import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class SendMoneyAccountScreen extends ConsumerStatefulWidget {
  const SendMoneyAccountScreen({super.key});

  @override
  ConsumerState<SendMoneyAccountScreen> createState() =>
      _SendMoneyAccountScreenState();
}

class _SendMoneyAccountScreenState
    extends ConsumerState<SendMoneyAccountScreen> {
  final accountNumberC = TextEditingController();
  final bankNameC = TextEditingController();

  final accountNumberF = FocusNode();

  @override
  void dispose() {
    accountNumberC.dispose();
    bankNameC.dispose();
    accountNumberF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: CustomAppbar(
        title: "Send Money",
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        children: [
          YBox(38),
          SendMoneyAccountTile(
            onTap: () {
              Navigator.pushNamed(context, RoutePath.sendMoneyAccountScreen);
            },
          ),
          YBox(50),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: "Amount to Withdraw ",
                      style: AppTypography.text12.withCustomColor(
                        AppColors.black.withValues(alpha: 0.45),
                      ),
                    ),
                    TextSpan(
                      text: "*",
                      style: AppTypography.text12
                          .withCustomColor(AppColors.red500),
                    ),
                  ],
                ),
              ),
              YBox(4),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(30),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: CustomTextField(
                        hideBorder: true,
                        hintText: "0.00",
                        hintStyle: AppTypography.text24.withCustomColor(
                          AppColors.yellowD7,
                        ),
                        prefixIcon: Padding(
                          padding: EdgeInsets.only(
                            left: Sizer.width(10),
                          ),
                          child: Text(
                            "₦",
                            style: AppTypography.text24.bold.withCustomColor(
                              AppColors.primaryBlue,
                            ),
                          ),
                        ),
                        onChanged: (p0) => setState(() {}),
                        onSubmitted: (value) {
                          accountNumberF.requestFocus();
                        },
                      ),
                    ),
                  ],
                ),
              ),
              YBox(4),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: "Available Balance: ",
                      style: AppTypography.text12.withCustomColor(
                        AppColors.black.withValues(alpha: 0.45),
                      ),
                    ),
                    TextSpan(
                      text: "₦ 120,940.40",
                      style: AppTypography.text12.bold
                          .withCustomColor(AppColors.primaryBlue),
                    ),
                  ],
                ),
              ),
            ],
          ),
          YBox(50),
          CustomTextField(
            controller: accountNumberC,
            focusNode: accountNumberF,
            showLabelHeader: true,
            labelText: "Remark",
            hintText: "Start typing",
            maxLines: 4,
            fillColor: AppColors.white,
            keyboardType: KeyboardType.phone,
            onChanged: (p0) => setState(() {}),
            onSubmitted: (value) {
              accountNumberF.requestFocus();
            },
          ),
          YBox(50),
          CustomBtn.solid(
            onTap: () {
              Navigator.pushNamed(context, RoutePath.sendMoneySuccesScreen);
            },
            text: "Confirm",
            outlineColor: AppColors.neutral5,
            onlineColor: AppColors.neutral3,
            textColor: AppColors.black.withValues(alpha: 0.25),
          ),
        ],
      ),
    );
  }
}
