import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class SendMoneySuccesScreen extends ConsumerStatefulWidget {
  const SendMoneySuccesScreen({super.key});

  @override
  ConsumerState<SendMoneySuccesScreen> createState() =>
      _SendMoneySuccesScreenState();
}

class _SendMoneySuccesScreenState extends ConsumerState<SendMoneySuccesScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: "Success",
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        child: Column(
          children: [
            const YBox(80),
            Center(
              child: SvgPicture.asset(
                AppSvgs.statusSuccess,
                height: Sizer.height(80),
              ),
            ),
            YBox(10),
            Text(
              "Withdrawal Successful",
              style: AppTypography.text18.bold.withCustomColor(
                AppColors.black.withValues(alpha: 0.85),
              ),
            ),
            YBox(8),
            Text(
              "Congratulations, your withdraw \n₦ 120,000 from your cashback balance is  successfully",
              textAlign: TextAlign.center,
              style: AppTypography.text14.withCustomColor(
                AppColors.black.withValues(alpha: 0.45),
              ),
            ),
            YBox(200),
            CustomBtn.solid(
              onTap: () {},
              // online: enableBtn,
              text: "Go Back Home",
            ),
          ],
        ),
      ),
    );
  }
}
