import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class ForgotPasswordOtpScreen extends ConsumerStatefulWidget {
  const ForgotPasswordOtpScreen({
    super.key,
    required this.arg,
  });
  final ForgotArg arg;

  @override
  ConsumerState<ForgotPasswordOtpScreen> createState() =>
      _ForgotPasswordOtpScreenState();
}

class _ForgotPasswordOtpScreenState
    extends ConsumerState<ForgotPasswordOtpScreen> {
  final _otpC = TextEditingController();
  final _otpF = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showAlertModal();
    });
  }

  @override
  void dispose() {
    _otpC.dispose();
    _otpF.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        bottom: false,
        child: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(16),
          ),
          children: [
            YBox(70),
            Center(
              child: Image.asset(
                AppImages.logo,
                height: Sizer.height(50),
              ),
            ),
            YBox(30),
            Center(
              child: Text(
                "Reset Your Password",
                style: AppTypography.text20.copyWith(
                  fontSize: Sizer.text(20),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            YBox(24),
            Text("Kindly enter the  reset password code sent \nto your mail",
                textAlign: TextAlign.center,
                style: AppTypography.text14.withCustomColor(
                  AppColors.black.withValues(alpha: 0.45),
                )),
            YBox(24),
            Pinput(
              defaultPinTheme: PinInputTheme.defaultPinTheme(),
              followingPinTheme: PinInputTheme.followPinTheme(),
              length: 6,
              controller: _otpC,
              focusNode: _otpF,
              showCursor: true,
              onChanged: (value) => setState(() {}),
              onCompleted: (pin) {
                // if (pin.length == 6) {
                //   FocusScope.of(context).unfocus();
                //
                // }
              },
            ),
            YBox(50),
            CustomBtn.solid(
              onTap: () {
                FocusScope.of(context).unfocus();
                if (_otpC.text.length == 6) {
                  Navigator.pushNamed(context, RoutePath.newPasswordScreen,
                      arguments: widget.arg.copyWith(
                        code: () => _otpC.text,
                      ));
                } else {
                  FlushBarToast.fLSnackBar(
                    snackBarType: SnackBarType.warning,
                    message: "Please enter a valid OTP",
                  );
                }
              },
              online: true,
              text: "Reset Password",
            ),
            YBox(26),
            Center(
              child: InkWell(
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.loginScreen);
                },
                child: Text(
                  "Back to Login",
                  style: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.primaryBlue,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _showAlertModal() {
    return ModalWrapper.showCustomDialog(context,
        child: AuthConfirmModal(
          title: "Password Reset Link Sent",
          desc: "A password reset link has been sent to your email.",
          iconPath: AppSvgs.info,
          confirmText: "Close",
          onConfirm: () {
            // Navigator.pushNamed(context, RoutePath.newPasswordScreen);
            Navigator.pop(context);
          },
        ));
  }
}
