import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final emailC = TextEditingController(text: "<EMAIL>");
  // final emailC = TextEditingController();
  final passwordC = TextEditingController();

  final emailF = FocusNode();
  final passwordF = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    emailC.dispose();
    passwordC.dispose();
    emailF.dispose();
    passwordF.dispose();

    super.dispose();
  }

  bool get isEmailValid =>
      emailC.text.trim().contains("@") && emailC.text.trim().contains(".");
  bool get isPasswordValid => passwordC.text.trim().isNotEmpty;
  bool get enableBtn => isEmailValid && isPasswordValid;

  @override
  Widget build(BuildContext context) {
    final authVm = ref.watch(authVmodel);
    return PopScope(
      canPop: false,
      child: BusyOverlay(
        show: authVm.isBusy,
        child: Scaffold(
          body: SafeArea(
            bottom: false,
            child: ListView(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(16),
              ),
              children: [
                YBox(70),
                Center(
                  child: Image.asset(
                    AppImages.logo,
                    height: Sizer.height(50),
                  ),
                ),
                YBox(30),
                Center(
                  child: Text(
                    "Log In",
                    style: AppTypography.text20.copyWith(
                      fontSize: Sizer.text(20),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                YBox(24),
                CustomTextField(
                  controller: emailC,
                  focusNode: emailF,
                  showLabelHeader: true,
                  labelText: "Email Address",
                  hintText: "Enter your email",
                  onChanged: (p0) => setState(() {}),
                  onSubmitted: (value) {
                    if (isEmailValid) {
                      passwordF.requestFocus();
                    }
                  },
                  errorText: emailC.text.isNotEmpty && !isEmailValid
                      ? "Invalid email"
                      : null,
                ),
                YBox(16),
                CustomTextField(
                  controller: passwordC,
                  focusNode: passwordF,
                  showLabelHeader: true,
                  labelText: "Password",
                  hintText: "Enter password",
                  isPassword: true,
                  onChanged: (p0) => setState(() {}),
                ),
                YBox(32),
                CustomBtn.solid(
                  onTap: () async {
                    FocusScope.of(context).unfocus();
                    final r = await ref.read(authVmodel).login(
                          identifier: emailC.text.trim(),
                          password: passwordC.text.trim(),
                        );

                    handleApiResponse(
                      response: r,
                      onSuccess: () {
                        Navigator.pushNamed(context, RoutePath.bottomNavScreen);
                      },
                    );
                  },
                  online: enableBtn,
                  // isLoading: authVm.isBusy,
                  text: "Log In",
                ),
                YBox(26),
                Center(
                  child: InkWell(
                    onTap: () {
                      Navigator.pushNamed(
                          context, RoutePath.forgotPasswordScreen);
                    },
                    child: Text(
                      "Forgot Password?",
                      style: AppTypography.text14.copyWith(
                        fontWeight: FontWeight.w500,
                        color: AppColors.red500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
