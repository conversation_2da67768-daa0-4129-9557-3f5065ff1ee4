import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class ForgotPasswordScreen extends ConsumerStatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  ConsumerState<ForgotPasswordScreen> createState() =>
      _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends ConsumerState<ForgotPasswordScreen> {
  final emailC = TextEditingController();
  final emailF = FocusNode();

  @override
  void dispose() {
    emailC.dispose();
    emailF.dispose();

    super.dispose();
  }

  bool get isEmailValid =>
      emailC.text.trim().contains("@") && emailC.text.trim().contains(".");

  @override
  Widget build(BuildContext context) {
    final authVm = ref.watch(authVmodel);
    return BusyOverlay(
      show: authVm.isBusy,
      child: Scaffold(
        body: SafeArea(
          bottom: false,
          child: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            children: [
              YBox(70),
              Center(
                child: Image.asset(
                  AppImages.logo,
                  height: Sizer.height(50),
                ),
              ),
              YBox(30),
              Center(
                child: Text(
                  "Reset Your Password",
                  style: AppTypography.text20.copyWith(
                    fontSize: Sizer.text(20),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              YBox(24),
              CustomTextField(
                controller: emailC,
                focusNode: emailF,
                showLabelHeader: true,
                labelText: "Email Address",
                hintText: "Enter your email",
                errorText: emailC.text.isNotEmpty && !isEmailValid
                    ? "Invalid email"
                    : null,
                onChanged: (p0) => setState(() {}),
                onSubmitted: (value) {
                  if (isEmailValid) {
                    FocusScope.of(context).unfocus();
                    _submit();
                  }
                },
              ),
              YBox(40),
              CustomBtn.solid(
                onTap: () {
                  // Navigator.pushNamed(context, RoutePath.newPasswordScreen);
                  FocusScope.of(context).unfocus();
                  _submit();
                },
                online: isEmailValid,
                text: "Reset Password",
              ),
              YBox(26),
              Center(
                child: InkWell(
                  onTap: () {
                    Navigator.pushNamed(context, RoutePath.loginScreen);
                  },
                  child: Text(
                    "Back to Login",
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _submit() async {
    final r = await ref
        .read(authVmodel)
        .resetPassword(identifier: emailC.text.trim());

    handleApiResponse(
      response: r,
      onSuccess: () {
        Navigator.pushNamed(context, RoutePath.forgotPasswordOtpScreen,
            arguments: ForgotArg(
              token: r.data,
              entity: "fulfilment-officer",
            ));
      },
    );
  }
}
