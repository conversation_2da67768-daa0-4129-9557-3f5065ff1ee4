import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class NewPasswordScreen extends ConsumerStatefulWidget {
  const NewPasswordScreen({
    super.key,
    required this.arg,
  });

  final ForgotArg arg;

  @override
  ConsumerState<NewPasswordScreen> createState() => _NewPasswordScreenState();
}

class _NewPasswordScreenState extends ConsumerState<NewPasswordScreen> {
  final passwordC = TextEditingController();
  final confirmPasswordC = TextEditingController();

  final passwordF = FocusNode();
  final confirmPasswordF = FocusNode();

  @override
  void dispose() {
    passwordC.dispose();
    confirmPasswordC.dispose();
    passwordF.dispose();
    confirmPasswordF.dispose();

    super.dispose();
  }

  bool get isPasswordValid => passwordC.text.trim().isNotEmpty;
  bool get isConfirmPasswordValid =>
      isPasswordValid && passwordC.text.trim() == confirmPasswordC.text.trim();
  bool get enableBtn => isPasswordValid && isConfirmPasswordValid;

  @override
  Widget build(BuildContext context) {
    return BusyOverlay(
      show: ref.watch(authVmodel).isBusy,
      child: Scaffold(
        body: SafeArea(
          bottom: false,
          child: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            children: [
              YBox(70),
              Center(
                child: Image.asset(
                  AppImages.logo,
                  height: Sizer.height(50),
                ),
              ),
              YBox(30),
              Center(
                child: Text(
                  "Create New Password",
                  style: AppTypography.text20.copyWith(
                    fontSize: Sizer.text(20),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              YBox(24),
              CustomTextField(
                controller: passwordC,
                focusNode: passwordF,
                showLabelHeader: true,
                isPassword: true,
                labelText: "New Password",
                hintText: "Enter password",
                onChanged: (p0) => setState(() {}),
              ),
              YBox(16),
              CustomTextField(
                controller: confirmPasswordC,
                focusNode: confirmPasswordF,
                showLabelHeader: true,
                isPassword: true,
                labelText: "Confirm Password",
                hintText: "Enter password",
                errorText:
                    confirmPasswordC.text.isNotEmpty && !isConfirmPasswordValid
                        ? "Passwords do not match"
                        : null,
                onChanged: (p0) => setState(() {}),
              ),
              YBox(32),
              CustomBtn.solid(
                onTap: () {
                  FocusScope.of(context).unfocus();
                  _submit();
                },
                online: enableBtn,
                text: "Reset Password",
              ),
              YBox(26),
              Center(
                child: InkWell(
                  onTap: () {
                    Navigator.pushNamed(context, RoutePath.loginScreen);
                  },
                  child: Text(
                    "Back to Login",
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _submit() async {
    final r = await ref.read(authVmodel).recoverPassword(
            forgotArg: ForgotArg(
          token: widget.arg.token,
          password: passwordC.text.trim(),
          confirmPassword: confirmPasswordC.text.trim(),
          code: widget.arg.code,
          entity: widget.arg.entity,
        ));

    handleApiResponse(
      response: r,
      onSuccess: () {
        Navigator.pushNamedAndRemoveUntil(
          context,
          RoutePath.loginScreen,
          (r) => false,
        );
      },
    );
  }
}
