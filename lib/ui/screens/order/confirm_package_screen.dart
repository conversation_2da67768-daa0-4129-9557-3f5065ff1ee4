import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class ConfirmPackageScreen extends ConsumerStatefulWidget {
  const ConfirmPackageScreen({super.key});

  @override
  ConsumerState<ConfirmPackageScreen> createState() =>
      _ConfirmPackageScreenState();
}

class _ConfirmPackageScreenState extends ConsumerState<ConfirmPackageScreen> {
  @override
  initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  _init() async {
    await ref.read(notificationVmodel).getNotifications();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: "Confirm Package",
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            YBox(18),
            Text(
              "Tick available products to confirm this package",
              style: AppTypography.text14.withCustomColor(
                AppColors.black.withValues(alpha: 0.45),
              ),
            ),
            Expanded(
              child: ListView.separated(
                shrinkWrap: true,
                padding: EdgeInsets.only(
                  top: Sizer.height(24),
                ),
                itemBuilder: (_, i) {
                  // final item = orderDetailVm.orderDetailModel?.lineItems?[i];
                  return Row(
                    children: [
                      SizedBox(
                        height: Sizer.height(40),
                        width: Sizer.width(40),
                        child: MyCachedNetworkImage(
                          imageUrl: "",
                        ),
                      ),
                      XBox(8),
                      Expanded(
                        child: Text(
                          "Ceramic Tiles (30) ",
                          style: AppTypography.text14.withCustomColor(
                            AppColors.black.withValues(alpha: 0.85),
                          ),
                        ),
                      ),
                      Text(
                        "2X",
                        style: AppTypography.text14.withCustomColor(
                          AppColors.black.withValues(alpha: 0.45),
                        ),
                      ),
                      XBox(30),
                      Text(
                        "N 3,900",
                        style: AppTypography.text14.withCustomColor(
                          AppColors.black.withValues(alpha: 0.45),
                        ),
                      ),
                    ],
                  );
                },
                separatorBuilder: (_, __) => YBox(16),
                itemCount: 4,
              ),
            ),
            YBox(24),
            CustomBtn.solid(
              onTap: () {
                Navigator.pop(context);
              },
              online: true,
              isOutline: true,
              textColor: AppColors.black.withValues(alpha: 0.45),
              outlineColor: AppColors.black.withValues(alpha: 0.45),
              text: "Confirm Package",
            ),
            YBox(30),
          ],
        ),
      ),
    );
  }
}
