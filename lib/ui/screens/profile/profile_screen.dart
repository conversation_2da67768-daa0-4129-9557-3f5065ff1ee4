import 'dart:io';

import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  File? _imageFile;
  bool loadingImage = false;
  String? avatarUrl;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  _init() async {
    final user = ref.read(authVmodel).user;

    if (user != null) {
      avatarUrl = user.avatar;
    }

    setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
  }

  // bool get emailIsEmpty => ref.watch(authVmodel).user?.email?.isEmpty ?? true;
  // bool get phoneIsEmpty => ref.watch(authVmodel).user?.phone?.isEmpty ?? true;

  bool get enableBtn => _imageFile != null;

  @override
  Widget build(BuildContext context) {
    final authVm = ref.watch(authVmodel);
    return SizedBox(
      width: Sizer.screenWidth,
      height: Sizer.screenHeight,
      child: Scaffold(
        appBar: CustomAppbar(
          title: "Profile",
          trailingWidget: InkWell(
              onTap: () {
                Navigator.pushNamed(context, RoutePath.notificationScreen);
              },
              child: SvgPicture.asset(AppSvgs.notification)),
          leadingWidget: Container(
            height: Sizer.height(40),
            width: Sizer.width(40),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(Sizer.radius(40)),
                border: Border.all(
                  color: AppColors.primaryBlue,
                  width: 2,
                )),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(Sizer.radius(40)),
              child: ref.watch(authVmodel).user?.avatar != null
                  ? MyCachedNetworkImage(
                      imageUrl: ref.watch(authVmodel).user!.avatar,
                      fit: BoxFit.cover,
                    )
                  : Icon(
                      Iconsax.user,
                      size: Sizer.width(20),
                    ),
            ),
          ),
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(16),
          ),
          child: Column(
            children: [
              const YBox(30),
              Container(
                padding: EdgeInsets.all(Sizer.radius(24)),
                decoration: BoxDecoration(
                  color: AppColors.dayBreakBlue,
                  borderRadius: BorderRadius.circular(Sizer.radius(16)),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: _pickImage,
                          child: Stack(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(
                                  Sizer.radius(100),
                                ),
                                child: Container(
                                  height: Sizer.height(96),
                                  width: Sizer.height(96),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: AppColors.neutral5,
                                      // color: avatarUrl == null
                                      //     ? AppColors.neutral5
                                      //     : AppColors.transparent,
                                      width: Sizer.width(2),
                                    ),
                                    borderRadius: BorderRadius.circular(
                                        Sizer.radius(100)),
                                  ),
                                  child: loadingImage ||
                                          ref
                                              .read(fileUploadVm)
                                              .busy(uploadState)
                                      ? LoaderIcon(
                                          size: 30,
                                          color: AppColors.neutral5,
                                        )
                                      : (_imageFile != null ||
                                              avatarUrl != null)
                                          ? MyCachedNetworkImage(
                                              imageUrl: avatarUrl!,
                                              fit: BoxFit.cover,
                                            )
                                          : Icon(
                                              Iconsax.user,
                                              size: Sizer.width(50),
                                            ),
                                ),
                              ),
                              Positioned(
                                bottom: 0,
                                right: 0,
                                child: InkWell(
                                  onTap: _pickImage,
                                  child: Container(
                                    padding: EdgeInsets.all(Sizer.radius(4)),
                                    decoration: BoxDecoration(
                                      color: AppColors.grayF2,
                                      borderRadius: BorderRadius.circular(
                                          Sizer.radius(4)),
                                    ),
                                    child: SvgPicture.asset(
                                      AppSvgs.pen,
                                      height: Sizer.height(16),
                                      width: Sizer.width(16),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    YBox(10),
                    Text(
                      authVm.user?.name ?? "",
                      style: AppTypography.text16.medium.withCustomColor(
                          AppColors.black.withValues(alpha: 0.85)),
                    ),
                    YBox(2),
                    Text(
                      authVm.user?.email ?? "",
                      style: AppTypography.text14.withCustomColor(
                          AppColors.black.withValues(alpha: 0.85)),
                    ),
                  ],
                ),
              ),
              YBox(20),
              ProfileListTile(
                title: (authVm.user?.activityStatus ?? "N/A").capitalizeFirst,
                trailingWidget: AvailabilityIndicator(
                  isOffline:
                      authVm.user?.activityStatus?.toLowerCase() == "offline",
                  color: authVm.user?.activityStatus == "available"
                      ? AppColors.green7
                      : authVm.user?.activityStatus == "busy"
                          ? AppColors.red22
                          : AppColors.transparent,
                ),
                onTap: () {
                  ModalWrapper.bottomSheet(
                    context: context,
                    widget: SetAvailabilityModal(
                      selectedAvailability: authVm.user?.activityStatus == null
                          ? null
                          : authVm.user!.activityStatus == "available"
                              ? AvailabilityType.available
                              : authVm.user!.activityStatus == "busy"
                                  ? AvailabilityType.busy
                                  : AvailabilityType.offline,
                    ),
                  );
                },
              ),
              YBox(12),
              ProfileListTile(
                title: "Reset Passkey",
                onTap: () {
                  // Navigator.pushNamed(context, RoutePath.editProfileScreen);
                },
              ),
              YBox(Sizer.screenHeight * 0.22),
              CustomBtn.solid(
                onTap: () async {
                  await ref.read(authVmodel).logout();
                },
                isLoading: authVm.isBusy,
                isOutline: true,
                outlineColor: AppColors.red4F,
                textStyle:
                    AppTypography.text16.withCustomColor(AppColors.red4F),
                text: "Log Out",
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _pickImage() async {
    setState(() => loadingImage = true);
    try {
      final pickedFile = await ImagePickerUtils.pickImage();

      if (pickedFile.isNotEmpty) {
        final croppedFile =
            await ImagePickerUtils.cropImage(image: pickedFile.first);
        if (croppedFile != null) {
          setState(() => _imageFile = croppedFile);

          final r =
              await ref.read(fileUploadVm).uploadFile(file: [croppedFile]);
          if (r.success && r.data != null && r.data!.isNotEmpty) {
            setState(() => avatarUrl = r.data!.first.url);
            final res = await ref.read(authVmodel).updateProfile(
                  avatar: _imageFile != null ? avatarUrl : null,
                );

            handleApiResponse(
              response: res,
              onSuccess: () {
                // Navigator.pushNamed(context, RoutePath.bottomNavScreen);
                ref.read(authVmodel).getUser();
              },
            );
          }
        }
      }
    } catch (e) {
      FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.warning, message: e.toString());
    } finally {
      setState(() => loadingImage = false);
    }
  }
}
