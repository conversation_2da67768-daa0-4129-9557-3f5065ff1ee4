import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class NotificationScreen extends ConsumerStatefulWidget {
  const NotificationScreen({super.key});

  @override
  ConsumerState<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends ConsumerState<NotificationScreen> {
  @override
  initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  _init() async {
    await ref.read(notificationVmodel).getNotifications();
  }

  // Group notifications by date categories
  Map<String, List<NotificationModel>> _groupNotificationsByDate(
      List<NotificationModel> notifications) {
    final Map<String, List<NotificationModel>> grouped = {};
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    for (final notification in notifications) {
      if (notification.createdAt == null) continue;

      final notificationDate = DateTime(
        notification.createdAt!.year,
        notification.createdAt!.month,
        notification.createdAt!.day,
      );

      String dateKey;
      if (notificationDate.isAtSameMomentAs(today)) {
        dateKey = "Today";
      } else if (notificationDate.isAtSameMomentAs(yesterday)) {
        dateKey = "Yesterday";
      } else {
        final difference = today.difference(notificationDate).inDays;
        if (difference < 7) {
          dateKey = "$difference days ago";
        } else if (difference < 14) {
          dateKey = "1 week ago";
        } else if (difference < 30) {
          final weeks = (difference / 7).floor();
          dateKey = "$weeks weeks ago";
        } else {
          final months = (difference / 30).floor();
          dateKey = months == 1 ? "1 month ago" : "$months months ago";
        }
      }

      if (!grouped.containsKey(dateKey)) {
        grouped[dateKey] = [];
      }
      grouped[dateKey]!.add(notification);
    }

    return grouped;
  }

  // Get relative time string
  String _getRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return "Just now";
    } else if (difference.inMinutes < 60) {
      final minutes = difference.inMinutes;
      return "$minutes min${minutes == 1 ? '' : 's'} ago";
    } else if (difference.inHours < 24) {
      final hours = difference.inHours;
      return "$hours hour${hours == 1 ? '' : 's'} ago";
    } else if (difference.inDays < 7) {
      final days = difference.inDays;
      return "$days day${days == 1 ? '' : 's'} ago";
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return "$weeks week${weeks == 1 ? '' : 's'} ago";
    } else {
      final months = (difference.inDays / 30).floor();
      return "$months month${months == 1 ? '' : 's'} ago";
    }
  }

  @override
  Widget build(BuildContext context) {
    final notificationVm = ref.watch(notificationVmodel);

    return Scaffold(
      appBar: CustomAppbar(
        title: "Notifications",
      ),
      body: LoadableContentBuilder<NotificationModel>(
        isBusy: notificationVm.isBusy,
        isError: notificationVm.hasError,
        items: notificationVm.notifications,
        loadingBuilder: (context) {
          return NotificationShimmer();
        },
        emptyBuilder: (context) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  AppSvgs.notification,
                  height: Sizer.height(80),
                  colorFilter: ColorFilter.mode(
                    AppColors.gray500,
                    BlendMode.srcIn,
                  ),
                ),
                YBox(16),
                Text(
                  "No notifications yet",
                  style: AppTypography.text16.medium.withCustomColor(
                    AppColors.gray500,
                  ),
                ),
                YBox(8),
                Text(
                  "You'll see notifications here when they arrive",
                  style: AppTypography.text14.withCustomColor(
                    AppColors.grayA4,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        },
        errorBuilder: (context) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "Error loading notifications",
                  style: AppTypography.text16.medium.withCustomColor(
                    AppColors.gray500,
                  ),
                ),
                YBox(20),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(32)),
                  child: CustomBtn.solid(
                    onTap: () {
                      _init();
                    },
                    online: true,
                    text: "Retry",
                  ),
                ),
              ],
            ),
          );
        },
        contentBuilder: (context) {
          final groupedNotifications =
              _groupNotificationsByDate(notificationVm.notifications);
          final sortedKeys = groupedNotifications.keys.toList()
            ..sort((a, b) {
              // Sort to show Today first, then Yesterday, then others
              if (a == "Today") return -1;
              if (b == "Today") return 1;
              if (a == "Yesterday") return -1;
              if (b == "Yesterday") return 1;
              return a.compareTo(b);
            });

          return RefreshIndicator(
            onRefresh: () async {
              await _init();
            },
            child: ListView.builder(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(16),
              ),
              itemCount: sortedKeys.length,
              itemBuilder: (context, index) {
                final dateKey = sortedKeys[index];
                final notifications = groupedNotifications[dateKey]!;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (index == 0) const YBox(40),
                    if (index > 0) const YBox(32),

                    // Date header
                    Row(
                      children: [
                        SvgPicture.asset(AppSvgs.calendar),
                        XBox(8),
                        Text(
                          dateKey.toUpperCase(),
                          style: AppTypography.text14.medium,
                        ),
                        XBox(8),
                        Expanded(child: HLine())
                      ],
                    ),
                    YBox(16),

                    // Notifications for this date
                    ListView.separated(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.zero,
                      itemCount: notifications.length,
                      separatorBuilder: (_, __) => YBox(16),
                      itemBuilder: (_, i) => NotificationCard(
                        notification: notifications[i],
                        relativeTime:
                            _getRelativeTime(notifications[i].createdAt!),
                      ),
                    ),

                    if (index == sortedKeys.length - 1) YBox(40),
                  ],
                );
              },
            ),
          );
        },
      ),
    );
  }
}
