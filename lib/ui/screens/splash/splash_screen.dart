import 'package:builder_konnect_mgt/core/core.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );

    _fadeAnimation =
        Tween<double>(begin: 0.0, end: 1.0).animate(_animationController);

    _animationController.forward();

    Future.delayed(const Duration(seconds: 4), () async {
      Navigator.of(context).pushReplacementNamed(RoutePath.loginScreen);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final themeVm = ref.watch(themeViewModel);
        bool isLightMode = themeVm.themeMode == ThemeMode.light;
        return Scaffold(
          backgroundColor: isLightMode ? Colors.black12 : Colors.white,
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              color: isLightMode ? AppColors.white : AppColors.black,
              child: Center(
                child: Image.asset(
                  AppImages.logo,
                  height: Sizer.height(50),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
