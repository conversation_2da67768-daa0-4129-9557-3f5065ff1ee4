import 'package:builder_konnect_mgt/core/core.dart';

class NotificationVm extends BaseVm {
  List<NotificationModel> _notifications = [];
  List<NotificationModel> get notifications => _notifications;

  Future<ApiResponse> getNotifications({
    String? unread,
    String? grouped,
    String? limit,
    String? paginate,
  }) async {
    UriBuilder uriBuilder = UriBuilder("/api/v1/shared/notifications")
      ..addQueryParameterIfNotEmpty("unread", unread ?? '')
      ..addQueryParameterIfNotEmpty("grouped", grouped ?? '')
      ..addQueryParameterIfNotEmpty("limit", limit ?? '')
      ..addQueryParameterIfNotEmpty("paginate", paginate ?? '');
    return await performApiCall(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _notifications = notificationModelFromJson(
            json.encode(data["data"]["notifications"]));
        return apiResponse;
      },
    );
  }
}

final notificationVmodel = ChangeNotifierProvider((ref) => NotificationVm());
