import 'package:builder_konnect_mgt/core/core.dart';

class RiderVm extends BaseVm {
  List<RiderDetails> _riderList = [];
  List<RiderDetails> get riderList => _riderList;

  Future<ApiResponse> getRiders({String? query}) async {
    UriBuilder uriBuilder =
        UriBuilder("/api/v1/fulfilment-officers/deliveries/all/riders")
          ..addQueryParameterIfNotEmpty("q", query ?? '');
    return await performApiCall(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _riderList = riderDetailsFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }
}

final riderVmodel = ChangeNotifierProvider((ref) {
  return RiderVm();
});
