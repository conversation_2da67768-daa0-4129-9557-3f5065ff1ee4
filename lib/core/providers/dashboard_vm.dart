import 'package:builder_konnect_mgt/core/core.dart';

class DashboardVm extends BaseVm {
  StatModel? _statModel;
  StatModel? get statModel => _statModel;

  Future<ApiResponse> getDashboardStats({String? dateFilter}) async {
    UriBuilder uriBuilder =
        UriBuilder("/api/v1/fulfilment-officers/dashboard/stats")
          ..addQueryParameterIfNotEmpty("date_filter", dateFilter ?? '');
    return await performApiCall(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      isFormData: true,
      onSuccess: (data) {
        _statModel = statModelFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }
}

final dashboardVmodel = ChangeNotifierProvider((ref) {
  return DashboardVm();
});
