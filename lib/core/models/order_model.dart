import 'dart:convert';

import 'package:builder_konnect_mgt/core/models/order_detail_model.dart';

List<OrderModel> orderModelFromJson(String str) =>
    List<OrderModel>.from(json.decode(str).map((x) => OrderModel.fromJson(x)));

String orderModelToJson(List<OrderModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class OrderModel {
  final String? id;
  final String? orderNumber;
  final String? receiptNo;
  final String? status;
  final Customer? customer;
  final DateTime? createdAt;

  OrderModel({
    this.id,
    this.orderNumber,
    this.receiptNo,
    this.status,
    this.customer,
    this.createdAt,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) => OrderModel(
        id: json["id"],
        orderNumber: json["order_number"],
        receiptNo: json["receipt_no"],
        status: json["status"],
        customer: json["customer"] == null
            ? null
            : Customer.fromJson(json["customer"]),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "order_number": orderNumber,
        "receipt_no": receiptNo,
        "status": status,
        "customer": customer?.toJson(),
        "created_at": createdAt?.toIso8601String(),
      };
}
