import 'dart:convert';

StatModel statModelFromJson(String str) => StatModel.fromJson(json.decode(str));

String statModelToJson(StatModel data) => json.encode(data.toJson());

class StatModel {
  final int? totalOrders;
  final int? pending;
  final int? inprogress;
  final int? completed;

  StatModel({
    this.totalOrders,
    this.pending,
    this.inprogress,
    this.completed,
  });

  factory StatModel.fromJson(Map<String, dynamic> json) => StatModel(
        totalOrders: json["total_orders"],
        pending: json["pending"],
        inprogress: json["inprogress"],
        completed: json["completed"],
      );

  Map<String, dynamic> toJson() => {
        "total_orders": totalOrders,
        "pending": pending,
        "inprogress": inprogress,
        "completed": completed,
      };
}
