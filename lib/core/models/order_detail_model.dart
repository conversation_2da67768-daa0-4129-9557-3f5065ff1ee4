import 'dart:convert';

OrderDetailModel orderDetailModelFromJson(String str) =>
    OrderDetailModel.fromJson(json.decode(str));

String orderDetailModelToJson(OrderDetailModel data) =>
    json.encode(data.toJson());

class OrderDetailModel {
  final String? id;
  final String? orderNumber;
  final String? receiptNo;
  final String? status;
  final DateTime? createdAt;
  final String? deliveryDate;
  final Customer? vendor;
  final Customer? customer;
  final List<LineItem>? lineItems;
  final Fees? fees;
  final String? subtotal;
  final String? amount;
  final RiderDetails? riderDetails;
  final List<String>? deliveryAttachments;
  final dynamic comment;
  final List<Timeline>? timeline;

  OrderDetailModel({
    this.id,
    this.orderNumber,
    this.receiptNo,
    this.status,
    this.createdAt,
    this.deliveryDate,
    this.vendor,
    this.customer,
    this.lineItems,
    this.fees,
    this.subtotal,
    this.amount,
    this.riderDetails,
    this.deliveryAttachments,
    this.comment,
    this.timeline,
  });

  factory OrderDetailModel.fromJson(Map<String, dynamic> json) =>
      OrderDetailModel(
        id: json["id"],
        orderNumber: json["order_number"],
        receiptNo: json["receipt_no"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        deliveryDate: json["delivery_date"],
        vendor:
            json["vendor"] == null ? null : Customer.fromJson(json["vendor"]),
        customer: json["customer"] == null
            ? null
            : Customer.fromJson(json["customer"]),
        lineItems: json["line_items"] == null
            ? []
            : List<LineItem>.from(
                json["line_items"]!.map((x) => LineItem.fromJson(x))),
        fees: json["fees"] == null ? null : Fees.fromJson(json["fees"]),
        subtotal: json["subtotal"],
        amount: json["amount"],
        riderDetails: json["rider_details"] == null
            ? null
            : RiderDetails.fromJson(json["rider_details"]),
        deliveryAttachments: json["delivery_attachments"] == null
            ? []
            : List<String>.from(json["delivery_attachments"]!.map((x) => x)),
        comment: json["comment"],
        timeline: json["timeline"] == null
            ? []
            : List<Timeline>.from(
                json["timeline"]!.map((x) => Timeline.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "order_number": orderNumber,
        "receipt_no": receiptNo,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "delivery_date": deliveryDate,
        "vendor": vendor?.toJson(),
        "customer": customer?.toJson(),
        "line_items": lineItems == null
            ? []
            : List<dynamic>.from(lineItems!.map((x) => x.toJson())),
        "fees": fees?.toJson(),
        "subtotal": subtotal,
        "amount": amount,
        "rider_details": riderDetails?.toJson(),
        "delivery_attachments": deliveryAttachments,
        "comment": comment,
        "timeline": timeline == null
            ? []
            : List<dynamic>.from(timeline!.map((x) => x.toJson())),
      };
}

class Customer {
  final String? name;
  final String? contact;
  final String? address;

  Customer({
    this.name,
    this.contact,
    this.address,
  });

  factory Customer.fromJson(Map<String, dynamic> json) => Customer(
        name: json["name"],
        contact: json["contact"],
        address: json["address"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "contact": contact,
        "address": address,
      };
}

class Fees {
  final int? tax;
  final int? serviceFee;

  Fees({
    this.tax,
    this.serviceFee,
  });

  factory Fees.fromJson(Map<String, dynamic> json) => Fees(
        tax: json["tax"],
        serviceFee: json["service_fee"],
      );

  Map<String, dynamic> toJson() => {
        "tax": tax,
        "service_fee": serviceFee,
      };
}

class LineItem {
  final String? mediaUrl;
  final String? product;
  final int? quantity;
  final String? totalCost;

  LineItem({
    this.mediaUrl,
    this.product,
    this.quantity,
    this.totalCost,
  });

  factory LineItem.fromJson(Map<String, dynamic> json) => LineItem(
        mediaUrl: json["media_url"],
        product: json["product"],
        quantity: json["quantity"],
        totalCost: json["total_cost"],
      );

  Map<String, dynamic> toJson() => {
        "media_url": mediaUrl,
        "product": product,
        "quantity": quantity,
        "total_cost": totalCost,
      };
}

List<RiderDetails> riderDetailsFromJson(String str) => List<RiderDetails>.from(
    json.decode(str).map((x) => RiderDetails.fromJson(x)));

class RiderDetails {
  final String? name;
  final String? email;
  final String? phone;
  final String? vehicleType;

  RiderDetails({
    this.name,
    this.email,
    this.phone,
    this.vehicleType,
  });

  factory RiderDetails.fromJson(Map<String, dynamic> json) => RiderDetails(
        name: json["name"],
        email: json["email"],
        phone: json["phone"],
        vehicleType: json["vehicle_type"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "email": email,
        "phone": phone,
        "vehicle_type": vehicleType,
      };
}

class Timeline {
  final String? status;
  final String? shortDescription;
  final String? longDescription;
  final String? time;

  Timeline({
    this.status,
    this.shortDescription,
    this.longDescription,
    this.time,
  });

  factory Timeline.fromJson(Map<String, dynamic> json) => Timeline(
        status: json["status"],
        shortDescription: json["short_description"],
        longDescription: json["long_description"],
        time: json["time"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "short_description": shortDescription,
        "long_description": longDescription,
        "time": time,
      };
}
