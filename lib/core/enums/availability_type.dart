import 'package:builder_konnect_mgt/core/core.dart';

enum AvailabilityType { available, busy, offline }

extension AvailabilityTypeExtension on AvailabilityType {
  Color get color {
    switch (this) {
      case AvailabilityType.available:
        return AppColors.green7;
      case AvailabilityType.busy:
        return AppColors.red22;
      case AvailabilityType.offline:
        return AppColors.transparent;
    }
  }

  String get label {
    switch (this) {
      case AvailabilityType.available:
        return "Available";
      case AvailabilityType.busy:
        return "Busy";
      case AvailabilityType.offline:
        return "Appear Offline";
    }
  }

  String get params {
    switch (this) {
      case AvailabilityType.available:
        return "available";
      case AvailabilityType.busy:
        return "busy";
      case AvailabilityType.offline:
        return "offline";
    }
  }
}
