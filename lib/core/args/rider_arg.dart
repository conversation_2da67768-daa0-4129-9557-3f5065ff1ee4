class RiderArg {
  final String name;
  final String phone;
  final String email;
  final String? vehicleType;
  final int? bankId;
  final String? accountNumber;
  final String? accountName;

  RiderArg({
    required this.name,
    required this.phone,
    required this.email,
    this.vehicleType,
    this.bankId,
    this.accountNumber,
    this.accountName,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'phone': phone,
      'email': email,
      'vehicle_type': vehicleType,
      'bank_id': bankId,
      'account_number': accountNumber,
      'account_name': accountName,
    };
  }
}
